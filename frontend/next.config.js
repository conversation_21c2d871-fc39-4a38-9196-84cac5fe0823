/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
    typedRoutes: true,
  },
  reactStrictMode:false,
  env: {
    API_URL: process.env.NEXT_PUBLIC_API_URL,
    HOST_IMAGE: process.env.NEXT_PUBLIC_HOST_IMAGE,
    SITE_URL: process.env.NEXT_PUBLIC_SITE_URL,
    PREFIX_API: process.env.NEXT_PUBLIC_PREFIX_API,
    SESSION_EXPIRES_DAYS: process.env.SESSION_EXPIRES_DAYS || '365',
    PREFETCH_PAGE: process.env.NEXT_PUBLIC_PREFETCH_PAGE,
    ASSETS_URL: process.env.NEXT_PUBLIC_ASSETS_URL,
    ENABLE_COOKIE_HTTPONLY: process.env.NEXT_PUBLIC_ENABLE_COOKIE_HTTPONLY,
  },
  async rewrites() {
    return [
      {
        // Rewrite for posts
        source: '/:slug((?!tag|author|category|posts|password/reset|storage|notfound).*)',
        destination: '/post/:slug',
      },
    ];
  },
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "images.pexels.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "images.unsplash.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: process.env.NEXT_PUBLIC_HOST_IMAGE,
        port: "",
        pathname: "/**",
      },
    ],
  },
};

module.exports = nextConfig;
