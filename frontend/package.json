{"name": "ncmaz-nextjs", "version": "0.2.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^5.0.1", "@next/env": "^15.2.1", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@types/lodash": "^4.14.201", "@types/node": "20.9.2", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@types/yup": "^0.32.0", "cookies-next": "^5.1.0", "dayjs": "^1.11.13", "eslint": "8.54.0", "eslint-config-next": "14.0.3", "framer-motion": "^10.16.5", "lodash": "^4.17.21", "next": "^14.2.25", "react": "18.2.0", "react-dom": "18.2.0", "react-hook-form": "^7.55.0", "react-hooks-global-state": "^2.1.0", "react-player": "^2.13.0", "react-swipeable": "^7.0.1", "react-toastify": "^11.0.5", "react-use": "^17.4.0", "react-use-keypress": "^1.3.1", "reading-time-estimator": "^1.11.0", "sass": "^1.69.5", "sharp": "^0.33.5", "swr": "^2.3.3", "tinycolor2": "^1.6.0", "typescript": "5.2.2", "yup": "^1.6.1", "zustand": "^5.0.3"}, "devDependencies": {"@types/tinycolor2": "^1", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "tailwindcss": "^3.3.5"}, "packageManager": "yarn@4.8.1"}