import { IThemeOptions } from '@/contains/types';

export const handleConfigMetaSeo = (dataMetaSeo: {
  slug?: string;
  indexFollow: string,
  type: 'post' | 'tag' | 'category' | 'author' | 'search' | ''
}) => {
  const { indexFollow, type, slug } = dataMetaSeo;
  const metaContent = indexFollow === 'index' ? 'index, follow' : 'noindex, nofollow';
  const canonicalUrl = `${process.env.SITE_URL}${!!type ? `/${type}` : ''}${!!slug ? `/${slug}` : ''}`;

  const robots = {
    robots: metaContent,
    googlebot: metaContent,
    bingbot: metaContent
  };
  return { robots, canonical: canonicalUrl };
};

export const handleConfigOpenGraph = (data: {
  themeData: IThemeOptions | null,
  titlePage: string,
  descriptionPage: string,
  canonical: string,
  siteTitle: string,
  altImage: string
  imageUrl: string;
}) => {
  const { themeData, titlePage, descriptionPage, canonical, siteTitle, altImage, imageUrl } = data;

  const openGraph = {
    title: titlePage,
    description: descriptionPage,
    url: canonical,
    siteName: siteTitle,
    images: [
      {
        url: imageUrl,
        width: 1200,
        height: 630,
        alt: altImage || themeData?.general.seo_title || ''
      }
    ],
    type: 'article'
  };
  const twitter = {
    card: 'summary_large_image',
    title: titlePage,
    description: descriptionPage,
    images: [imageUrl]
  };
  return { openGraph, twitter };
};
