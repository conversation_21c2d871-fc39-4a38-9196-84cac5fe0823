export async function clientCreateCookie(token: string) {
  const siteURL = process.env.SITE_URL;
  await fetch(`${siteURL}/authentication/cookie`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ token })
  });
}

export async function clientClearCookie() {
  const siteURL = process.env.SITE_URL;
  await fetch(`${siteURL}/authentication/cookie`, {
    method: 'DELETE'
  });
}
