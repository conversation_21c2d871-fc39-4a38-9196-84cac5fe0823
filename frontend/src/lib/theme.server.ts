import { themeApi } from '@/apis/themeApi';
import { IFooterConfig, IMenuItem, IThemeOptions } from '@/contains/types';
import { KEY_SWR } from '@/contains/keySWR';

export const getThemeOption = async () => {
  const res = await themeApi.get<IThemeOptions>({
    endpoint: KEY_SWR.GET_THEME_OPTIONS
  });
  return res.data || null;
};

export const getMenu = async () => {
  return await themeApi.getMenu<IMenuItem[]>({
    endpoint: KEY_SWR.GET_MENUS
  });
};

export const getFooter = async () => {
  return await themeApi.getFooter<IFooterConfig[]>({
    endpoint: KEY_SWR.GET_FOOTERS
  });
};
