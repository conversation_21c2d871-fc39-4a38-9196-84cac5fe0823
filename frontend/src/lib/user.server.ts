import { authServerApi } from '@/apis/authServerApi';
import { IUser } from '@/contains/author';
import { clientClearCookie } from '@/lib/clientCookie';

export const getUserData =
  async () => {
    const res = await authServerApi.get<IUser>({
      endpoint: 'me'
    });
    if (!!res.error && !res.data) {
      await clientClearCookie();
    }
    return res || {
      data: null,
      error: null,
      meta: false
    };
  };
