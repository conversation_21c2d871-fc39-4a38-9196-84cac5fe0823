import { expiresAt } from '@/contains/cookie';
import { cookies } from 'next/headers';

const enableHttpOnly = process.env.ENABLE_COOKIE_HTTPONLY === 'true';

export function createCookie(token: string) {
  const cookieStore = cookies();
  cookieStore.set('token', token, {
    httpOnly: enableHttpOnly,
    secure: true,
    sameSite: 'strict',
    path: '/',
    expires: expiresAt,
  });
}

export function clearCookie() {
  const cookieStore = cookies();

  cookieStore.set('token' +
    '' +
    '', '', {
    httpOnly: enableHttpOnly,
    secure: true,
    sameSite: 'strict',
    path: '/',
    expires: new Date(0),
  });
}

export function getToken() {
  const cookieStore = cookies();
  return cookieStore.get('token')?.value;
}
