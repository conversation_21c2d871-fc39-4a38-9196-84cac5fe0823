'use client';

import { fetcher } from '@/apis/index';
import { getCookie } from 'cookies-next/client';

export const interactionApi = {
  get: async <T = any>({
    endpoint = '',
    params
  }: {
    endpoint?: string;
    params?: Record<string, string | number | string[] | number[] | undefined>;
  }): Promise<{data: T | null; error: any; meta: any}> => {
    const token = getCookie('token');
    const path = endpoint ? `interaction/${endpoint}` : 'interaction';
    return fetcher<T>({ endpoint: path, params, headers: !!token ? { Authorization: `Bearer ${token}` } : undefined });
  },
  post: async <T = any>({
    endpoint = '',
    payload = null
  }: {
    endpoint?: string;
    payload?: BodyInit | null;
  }): Promise<{data: T | null; error: any; meta: any, message?: string}> => {
    const token = getCookie('token');
    const path = endpoint ? `interaction/${endpoint}` : 'interaction';
    return fetcher<T>({
      endpoint: path,
      method: 'POST',
      body: payload,
      headers: !!token ? { Authorization: `Bearer ${token}` } : undefined
    });
  },
  put: async <T = any>({
    endpoint = '',
    payload = null
  }: {
    endpoint?: string;
    payload?: BodyInit | null;
  }): Promise<{data: T | null; error: any; meta: any}> => {
    const token = getCookie('token');
    const path = endpoint ? `interaction/${endpoint}` : 'interaction';
    return fetcher<T>({
      endpoint: path,
      method: 'PUT',
      body: payload,
      headers: !!token ? { Authorization: `Bearer ${token}` } : undefined
    });
  },
  delete: async <T = any>({
    endpoint = '',
    payload = null
  }: {
    endpoint?: string;
    payload?: BodyInit | null;
  }): Promise<{data: T | null; error: any; meta: any}> => {
    const token = getCookie('token');
    const path = endpoint ? `interaction/${endpoint}` : 'interaction';
    return fetcher<T>({
      endpoint: path,
      method: 'DELETE',
      body: payload,
      headers: !!token ? { Authorization: `Bearer ${token}` } : undefined
    });
  }
};
