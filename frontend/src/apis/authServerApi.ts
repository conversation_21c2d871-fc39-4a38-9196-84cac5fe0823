import { fetcher } from '@/apis/index';
import { getToken } from '@/lib/cookie';
import { clientClearCookie } from '@/lib/clientCookie';

export const authServerApi = {

  get: async <T = any>({
    params,
    endpoint = ''
  }: {
    params?: Record<string, string | number | string[] | number[] | undefined>;
    endpoint?: string;
  }): Promise<{data: T | null; error: any; meta: any}> => {
    const token = getToken();
    const path = endpoint ? `auth/${endpoint}` : 'auth';
    const res = await fetcher<T>({
      endpoint: path,
      params,
      headers: !!token ? { Authorization: `Bearer ${token}` } : undefined
    });
    if (!!res.error && !res.data) {
      await clientClearCookie();
    }
    return res;
  }
};
