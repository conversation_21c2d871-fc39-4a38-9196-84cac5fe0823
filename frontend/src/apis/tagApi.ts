import { fetcher } from '@/apis/index';

export const tagApi = {
  get: async <T = any>({
    params,
    endpoint = '',
  }: {
    params?: Record<string, string | number | string[] | number[] | undefined>;
    endpoint?: string;
  }): Promise<{data: T | null; error: any; meta: any}> => {
    const path = endpoint ? `tags/${endpoint}` : 'tags';
    return fetcher<T>({ endpoint: path, params });
  }
};

