import { fetcher } from '@/apis/index';
import { getToken } from '@/lib/cookie';

export const postServerApi = {
  get: async <T = any>({
    params,
    endpoint = '',
    headers,
  }: {
    params?: Record<string, string | number | string[] | number[] | undefined>;
    endpoint?: string;
    headers?: Record<string, string>;
  }): Promise<{data: T | null; error: any; meta: any}> => {
    const path = endpoint ? `posts/${endpoint}` : 'posts';
    const cookieToken = getToken();

    return fetcher<T>({
      endpoint: path,
      params,
      headers: { ...headers, ...( cookieToken ? { Authorization: `Bearer ${cookieToken}` } : {} ) },
    });
  },
  post: async <T = any>({
    params,
    endpoint = '',
    payload = null,
    headers
  }: {
    params?: Record<string, string | number | string[] | number[] | undefined>;
    endpoint?: string;
    payload?: BodyInit | null;
    headers?: Record<string, string>;
  }): Promise<{data: T | null; error: any; meta: any}> => {
    const path = endpoint ? `posts/${endpoint}` : 'posts';
    const cookieToken = getToken();

    return fetcher<T>({
      endpoint: path,
      params,
      method: 'POST',
      body: payload,
      headers: { ...headers, ...( cookieToken ? { Authorization: `Bearer ${cookieToken}` } : {} ) }
    });
  }
};
