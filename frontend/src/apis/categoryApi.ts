import { fetcher } from '@/apis/index';

export const categoryApi = {
  get: async <T = any>({
    params,
    endpoint = '',
  }: {
    params?: Record<string, string | number | string[] | number[] | undefined>;
    endpoint?: string;
  }): Promise<{data: T | null; error: any; meta: any}> => {
    const path = endpoint ? `categories/${endpoint}` : 'categories';
    return fetcher<T>({ endpoint: path, params });
  },
  getCustom: async <T = any>({
    params,
    endpoint = '',
  }: {
    params?: Record<string, string | number | string[] | number[] | undefined>;
    endpoint?: string;
  }): Promise<{data: T | null; error: any; meta: any}> => {
    const path = endpoint ? `categories/custom/${endpoint}` : 'categories';
    return fetcher<T>({ endpoint: path, params });
  }
};

