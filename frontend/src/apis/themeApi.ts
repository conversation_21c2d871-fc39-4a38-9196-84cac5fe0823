import { fetcher } from '@/apis/index';

export const themeApi = {
  get: async <T = any>({
    endpoint = ''
  }: {
    endpoint?: string;
  }): Promise<{data: T | null; error: any; meta: any}> => {
    return fetcher<T>({ endpoint });
  },
  getMenu: async <T = any>({
    endpoint = ''
  }: {
    endpoint?: string;
  }): Promise<{data: T | null; error: any; meta: any}> => {
    return fetcher<T>({ endpoint });
  },
  getFooter: async <T = any>({
    endpoint = ''
  }: {
    endpoint?: string;
  }): Promise<{data: T | null; error: any; meta: any}> => {
    return fetcher<T>({ endpoint });
  }
};

