'use client';
import { fetcher } from '@/apis/index';
import { getCookie } from 'cookies-next/client';

export const authApi = {
  get: async <T = any>({
    params,
    endpoint = '',
  }: {
    params?: Record<string, string | number | string[] | number[] | undefined>;
    endpoint?: string;
  }): Promise<{data: T | null; error: any; meta: any}> => {
    const path = endpoint ? `auth/${endpoint}` : 'auth';
    return fetcher<T>({ endpoint: path, params });
  },
  post: async <T = any>({
    params,
    endpoint = '',
    payload = null
  }: {
    params?: Record<string, string | number | string[] | number[] | undefined>;
    endpoint?: string;
    payload?: BodyInit | null;
  }): Promise<{data: T | null; error: any; meta: any}> => {
    const token = getCookie('token');
    const path = endpoint ? `auth/${endpoint}` : 'auth';
    return fetcher<T>({ endpoint: path, params, method: 'POST', body: payload, headers: !!token ? { Authorization: `Bearer ${token}` } : undefined });
  },
  put: async <T = any>({
    params,
    endpoint = '',
    payload = null
  }: {
    params?: Record<string, string | number | string[] | number[] | undefined>;
    endpoint?: string;
    payload?: BodyInit | null;
  }): Promise<{data: T | null; error: any; meta: any}> => {
    const token = getCookie('token');
    const path = endpoint ? `auth/${endpoint}` : 'auth';
    return fetcher<T>({ endpoint: path, params, method: 'PUT', body: payload, headers: !!token ? { Authorization: `Bearer ${token}` } : undefined });
  },
  postFile: async <T = any>({
    params,
    endpoint = '',
    payload = null
  }: {
    params?: Record<string, string | number | string[] | number[] | undefined>;
    endpoint?: string;
    payload?: BodyInit | null;
  }): Promise<{data: T | null; error: any; meta: any}> => {
    const token = getCookie('token');
    const path = endpoint ? `auth/${endpoint}` : 'auth';
    return fetcher<T>({ endpoint: path, params, method: 'POST', body: payload, headers: !!token ? { Authorization: `Bearer ${token}` } : undefined });
  }
};
