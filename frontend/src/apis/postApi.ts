import { fetcher } from '@/apis/index';

export const postApi = {
  get: async <T = any>({
    params,
    endpoint = '',
    headers,
  }: {
    params?: Record<string, string | number | string[] | number[] | undefined>;
    endpoint?: string;
    headers?: Record<string, string>;
  }): Promise<{data: T | null; error: any; meta: any}> => {
    const path = endpoint ? `posts/${endpoint}` : 'posts';
    return fetcher<T>({ endpoint: path, params, headers });
  },
  post: async <T = any>({
    params,
    endpoint = '',
    payload = null
  }: {
    params?: Record<string, string | number | string[] | number[] | undefined>;
    endpoint?: string;
    payload?: BodyInit | null;
  }): Promise<{data: T | null; error: any; meta: any}> => {
    const path = endpoint ? `posts/${endpoint}` : 'posts';
    return fetcher<T>({ endpoint: path, params, method: 'POST', body: payload });
  }
};
