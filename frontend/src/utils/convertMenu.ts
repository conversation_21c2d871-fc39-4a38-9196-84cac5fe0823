import { IFooterConfig, IMenuItem, INavItemType } from '@/contains/types';
import _ from 'lodash';
import { WidgetFooterMenu } from '@/components/Footer/Footer';

const randomId = _.uniqueId;

export const convertMenusToNavItems = (menus: IMenuItem[]) => {
  const nodes = menus.filter((menu) =>
    menu.location.some((loc) => loc.location === 'main-menu')
  ).flatMap((menu) => menu.menu_nodes);

  return nodes.map((node) => ( {
    id: randomId(),
    name: node.title,
    href: node.url
  } as INavItemType ));
};

export const convertMenusFooterItems = (
  menus: IMenuItem[],
  footerConfigs: IFooterConfig[]
): WidgetFooterMenu[] => {
  const allowedMenuIds = Array.from(new Set(footerConfigs.map((item) => item.menu_id)));

  return menus
  .filter(
    (menu) =>
      !menu.location.some((loc) => loc.location === 'main-menu') &&
      allowedMenuIds.includes(menu.slug)
  )
  .map((menu) => ({
    id: String(menu.id),
    title: menu.name,
    menus: [...menu.menu_nodes]
    .sort((a, b) => a.position - b.position)
    .map((node) => ({
      href: node.url,
      label: node.title,
    })),
  })) as WidgetFooterMenu[];
};
