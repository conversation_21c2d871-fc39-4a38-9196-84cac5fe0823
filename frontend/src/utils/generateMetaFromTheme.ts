import { IThemeOptions } from '@/contains/types';

export function generateMetaFromTheme(theme: IThemeOptions | null) {
  if (!theme) {
    return {
      title: 'Cuuduatin',
      description: 'Cuuduatin',
      favicon:  'favicon.ico'
    };
  }
  const general = theme?.general;
  const logo = theme?.logo;
  if (!general) {
    return null;
  }

  const { seo_title, site_title, seo_description, site_description } = general;

  const separator = general.site_title_separator || '-';
  const siteTitle = site_title || 'Cuuduatin';

  const fullTitle = seo_title || `Trang chủ ${separator} ${siteTitle} `;
  const favicon = logo?.favicon || '';

  const description = seo_description || site_description || seo_title;

  return {
    title: fullTitle,
    description,
    favicon
  };
}
