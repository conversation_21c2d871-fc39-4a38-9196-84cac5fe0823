import { LightnessMode } from '@/contains/types';

export function getLightnessMap(mode: LightnessMode = 'balanced'): Record<number, number> {
  switch (mode) {
    case 'too-soft':
      return {
        50:  60,
        100: 50,
        200: 40,
        300: 30,
        400: 20,
        500: 0,
        600: 2,
        700: 4,
        800: 6,
        900: 8,
      };

    case 'soft':
      return {
        50:  36,
        100: 30,
        200: 22,
        300: 14,
        400: 6,
        500: 0,
        600: 4,
        700: 8,
        800: 12,
        900: 16,
      };

    case 'balanced':
      return {
        50:  30,
        100: 24,
        200: 18,
        300: 10,
        400: 5,
        500: 0,
        600: 6,
        700: 14,
        800: 22,
        900: 30,
      };

    case 'strong':
      return {
        50:  45,
        100: 35,
        200: 25,
        300: 15,
        400: 8,
        500: 0,
        600: 10,
        700: 22,
        800: 34,
        900: 48,
      };

    case 'too-strong':
      return {
        50:  55,
        100: 42,
        200: 30,
        300: 18,
        400: 10,
        500: 0,
        600: 12,
        700: 26,
        800: 40,
        900: 60,
      };

    case 'reverse-soft':
      return {
        50: 16,
        100: 12,
        200: 8,
        300: 4,
        400: 2,
        500: 0,
        600: 6,
        700: 12,
        800: 20,
        900: 30,
      };

    case 'reverse-balanced':
      return {
        50: 30,
        100: 22,
        200: 14,
        300: 6,
        400: 3,
        500: 0,
        600: 5,
        700: 10,
        800: 16,
        900: 24,
      };

    case 'reverse-strong':
      return {
        50: 48,
        100: 36,
        200: 28,
        300: 18,
        400: 10,
        500: 0,
        600: 8,
        700: 16,
        800: 24,
        900: 32,
      };

    default:
      throw new Error(`Invalid lightness mode: ${mode}`);
  }
}
