import tinycolor from 'tinycolor2';
import { getLightnessMap } from '@/utils/getLightnessMap';

function hexToRgbString(hex: string): string {
  const c = tinycolor(hex).toRgb();
  return `${c.r}, ${c.g}, ${c.b}`;
}

function RGBtoHex(rgb: string): string {
  const c = tinycolor(rgb).toRgb();
  return `#${c.r.toString(16).padStart(2, '0')}${c.g.toString(16).padStart(2, '0')}${c.b.toString(16).padStart(2, '0')}`;
}

export function generateTailwindShades(baseColor: string): Record<number, string> {
  const levels = [50, 100, 200, 300, 400, 500, 600, 700, 800, 900];

  const lightnessMap = getLightnessMap('too-soft');

  const result: Record<number, string> = {};

  levels.forEach(level => {
    const amount = lightnessMap[level];
    const color = level <= 500
      ? tinycolor(baseColor).lighten(amount)
      : tinycolor(baseColor).darken(amount);

    result[level] = color.toHexString();
  });

  return result;
}

export function generateColorCSSVariables(baseColor: string): string {
  const shades = generateTailwindShades(baseColor);

  return `
    :root {
      ${Object.entries(shades).map(([key, hex]) => `--c-primary-${key}: ${hexToRgbString(hex)} !important;`).join('\n')}
    }
  `;
}

export const getColorClass = (color: string) => {
  switch (color) {
    case 'pink':
      return 'bg-pink-500';
    case 'red':
      return 'bg-red-500';
    case 'gray':
      return 'bg-gray-500';
    case 'green':
      return 'bg-green-500';
    case 'purple':
      return 'bg-purple-500';
    case 'indigo':
      return 'bg-indigo-500';
    case 'yellow':
      return 'bg-yellow-500';
    case 'blue':
      return 'bg-blue-500';
    default:
      return 'bg-primary-500';
  }
};

export const getColorClassWithHover = (hasHover = true, color: string) => {
  switch (color) {
    case 'pink':
      return `text-pink-800 bg-pink-100 ${
        hasHover ? 'hover:bg-pink-800' : ''
      }`;
    case 'red':
      return `text-red-800 bg-red-100 ${hasHover ? 'hover:bg-red-800' : ''}`;
    case 'gray':
      return `text-gray-800 bg-gray-100 ${
        hasHover ? 'hover:bg-gray-800' : ''
      }`;
    case 'green':
      return `text-green-800 bg-green-100 ${
        hasHover ? 'hover:bg-green-800' : ''
      }`;
    case 'purple':
      return `text-purple-800 bg-purple-100 ${
        hasHover ? 'hover:bg-purple-800' : ''
      }`;
    case 'indigo':
      return `text-indigo-800 bg-indigo-100 ${
        hasHover ? 'hover:bg-indigo-800' : ''
      }`;
    case 'yellow':
      return `text-yellow-800 bg-yellow-100 ${
        hasHover ? 'hover:bg-yellow-800' : ''
      }`;
    case 'blue':
      return `text-blue-800 bg-blue-100 ${
        hasHover ? 'hover:bg-blue-800' : ''
      }`;
    default:
      return `text-primary-800 bg-primary-100 ${
        hasHover ? 'hover:bg-primary-800' : ''
      }`;
  }
};

export const getColorCustom = (color: string) => {
  const baseColor = tinycolor(color);
  const isLightColor = baseColor.isLight();

  const adjustedColor = isLightColor
    ? baseColor.darken(25)
    : baseColor.lighten(35);

  return {
    baseColor: RGBtoHex(color),
    adjustedColor: adjustedColor.toHexString()
  };
};
