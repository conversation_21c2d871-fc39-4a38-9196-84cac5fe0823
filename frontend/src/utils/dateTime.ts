import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import 'dayjs/locale/vi';

dayjs.extend(customParseFormat);

const DEFAULT_FORMAT = 'DD/MM/YYYY';

const formatMap: Record<string, string> = {
  'M d, Y': 'MMM D, YYYY',
  'F j, Y': 'MMMM D, YYYY',
  'F d, Y': 'MMMM DD, YYYY',
  'Y-m-d': 'YYYY-MM-DD',
  'Y-M-d': 'YYYY-MMM-DD',
  'd-m-Y': 'DD-MM-YYYY',
  'd-M-Y': 'DD-MMM-YYYY',
  'm/d/Y': 'MM/DD/YYYY',
  'M/d/Y': 'MMM/DD/YYYY',
  'd/m/Y': 'DD/MM/YYYY',
  'd/M/Y': 'DD/MMM/YYYY',
  'd.m.Y': 'DD.MM.YYYY'
};

export const formatDate = (data: {dateString: string | Date; isTime?: boolean; formatType?: string}) => {
  const { dateString, isTime = false, formatType = DEFAULT_FORMAT } = data;

  if (!dateString) {
    return '-';
  }

  const formatsToTry = formatType ? [formatType] : Object.keys(formatMap);

  let parsedDate = null;

  for (const fmt of formatsToTry) {
    const dayjsFormat = formatMap[fmt] || fmt;
    const attempt = dayjs(dateString, dayjsFormat, true);
    if (attempt.isValid()) {
      parsedDate = attempt;
      break;
    }
  }

  if (!parsedDate) {
    const fallbackDate = dayjs(dateString);
    if (fallbackDate.isValid()) {
      parsedDate = fallbackDate;
    } else {
      return '-';
    }
  }

  const outputFormat = formatType && formatMap[formatType]
    ? formatMap[formatType]
    : DEFAULT_FORMAT;

  return parsedDate.format(isTime ? `${outputFormat} HH:mm` : outputFormat);
};
