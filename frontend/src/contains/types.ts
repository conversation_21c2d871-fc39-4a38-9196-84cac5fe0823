export interface IResponse<T> {
  success: boolean;
  data: T;
}

export type TAuthResponse = IResponse<{
  refresh_token: string;
  access_token: string;
  data: any;
}>;

export interface IReaction {
  likes_count: string;
}

export interface IResponseReaction {
  total_like: string;
}

export const TABS = [
  { name: '<PERSON>à<PERSON> viết', value: 'post' },
  { name: '<PERSON><PERSON> mục', value: 'categories' },
  { name: 'Thẻ', value: 'tags' }
];
export const FILTERS = [
  { name: 'Mới nhất', value: 'desc' },
  { name: 'Nổi bật nhất', value: 'total_score' },
  { name: '<PERSON>hi<PERSON>u bình luận nhất', value: 'total_comments' },
  { name: 'Xem nhiều nhất', value: 'total_views' }
];

export interface IMetaSeo {
  id: number,
  meta_key: string;
  meta_value: IMetaValue[],
  reference_id: 1,
  reference_type: string;
  created_at: string;
  updated_at: string;
  meta_value_custom: IMetaValue
}

export interface IMetaValue {
  seo_title: string;
  seo_description: string;
  index: string;
}

export type LightnessMode =
  | 'too-soft'
  | 'soft'
  | 'balanced'
  | 'strong'
  | 'too-strong'
  | 'reverse-soft'
  | 'reverse-balanced'
  | 'reverse-strong';

// unit is milliseconds
export const REVALIDATE = {
  ONE_HOUR: 3600,                    // 60 × 60
  ONE_DAY: 86400,                    // 24 × 60 × 60
  ONE_WEEK: 604800,                  // 7 × 86400
  ONE_MONTH: 2592000,                // 30 × 86400 (≈ 30 days)
  ONE_YEAR: 31536000,                // 365 × 86400
  TWO_YEARS: 63072000               // 2 × 365 × 86400
};

export interface IBlogData {
  blog_page: {
    id: number;
    name: string;
    content: string;
    user_id: number;
    image: string | null;
    template: string;
    description: string | null;
    status: {
      value: string;
      label: string;
    };
    created_at: string;
    updated_at: string;
  };
  number_of_posts_in_a_category: string;
  number_of_posts_in_a_tag: string;
}

export interface ITypography {
  tp_primary_font: string;
  tp_h1_size: number;
  tp_h2_size: number;
  tp_h3_size: number;
  tp_h4_size: number;
  tp_h5_size: number;
  tp_h6_size: number;
  tp_body_size: number;
}

export interface IGeneral {
  primary_color: string;
  primary_color_hover: string;
  site_description: string;
  address: string;
  website: string;
  contact_email: string;
  site_title: string;
  site_title_separator: '-',
  seo_title: string;
  seo_description: string;
  seo_index: boolean,
  seo_og_image: string,
  term_and_privacy_policy_url: '',
  copyright: string;
  date_format: string;
}

export interface IThemeOptions {
  page: null;
  logo: {
    logo_height: number;
    favicon: string;
    logo: string;
  };
  general: IGeneral,
  blog: IBlogData;
  typography: ITypography,
  social_links: Array<[
    {
      key: 'social-name';
      value: string;
    },
    {
      key: 'social-icon';
      value: string;
    },
    {
      key: 'social-url';
      value: string;
    }
  ]>;
  cookie_consent: {
    cookie_consent_enable: 'yes' | 'no';
    cookie_consent_message: string;
  };
}

export interface IMenuStatus {
  value: string;
  label: string;
}

export interface IMenuLocation {
  menu_id: number;
  location: string;
}

export interface IMenuNode {
  url: string;
  icon_font: string | null;
  position: number;
  title: string;
  css_class: string | null;
  target: '_self' | '_blank' | string;
  has_child: number;
}

export interface IMenuItem {
  id: number;
  name: string;
  slug: string;
  status: IMenuStatus;
  menu_nodes: IMenuNode[];
  location: IMenuLocation[];
}

export interface IMenuResponse {
  data: IMenuItem[];
  error: boolean;
  message: string | null;
}

export interface INavItemType {
  id: string;
  href?: string;
  name: string;
  type?: 'dropdown' | 'megaMenu';
  isNew?: boolean;
  children?: INavItemType[];
}

export interface IFooterConfig {
  name: string;
  position: number;
  menu_id: string;
}
