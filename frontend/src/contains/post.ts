import { Route } from '@/routers/types';
import { TCategory } from '@/contains/category';
import { TTag } from '@/contains/tag';
import { TAuthor } from '@/contains/author';

export type TResponse = {
  links?: {
    first: string;
    last: string;
    prev: string;
    next: string;
  };
  meta?: ApiResponseMeta;
  error?: boolean;
  message?: string | null;
}

export interface IPost extends TResponse {
  data?: Post[];
}

export interface IPostDetail extends TResponse {
  data?: Post;
}

export type ApiResponseMeta = {
  current_page: number;
  from: number;
  last_page: number;
  total: number;
  links: any[];
}
export type TAuthorComment = {
  first_name: string;
  full_name: string;
  id: number
  image: string;
  last_name: string;
  role: string | null
}
export type TCommentItem = {
  id: number;
  author?: TAuthorComment;
  content: string;
  status?: {
    value: string;
    label: string;
  }
  is_liked: boolean,
  likes_count: string;
  website?: string;
  replies?:TCommentItem[]
}
export type TCommentResponse = {
  data: TCommentItem[]
}

export type Post = {
  id: number;
  name: string;
  slug: Route;
  description: string;
  image: string;
  categories: TCategory[];
  tags: TTag[];
  author: TAuthor;
  created_at: string;
  updated_at: string;
  content: string;
  likes_count: string;
  comments_count: number;
  is_liked: boolean;
  is_commented: boolean;
  comments: TCommentResponse;
};
