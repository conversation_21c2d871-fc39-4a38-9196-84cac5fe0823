import { Post } from '@/contains/post';

export type TResponse = {
  links?: {
    first: string;
    last: string;
    prev: string;
    next: string;
  };
  meta?: ApiResponseMeta;
  error?: boolean;
  message?: string | null;
}

export interface IAuthor extends TResponse {
  data?: TAuthor[];
}

export interface IAuthorDetail extends TResponse {
  data?: TAuthor;
}

export type ApiResponseMeta = {
  current_page: number;
  from: number;
  last_page: number;
  total: number;
  links: any[];
}

export type TAuthor = {
  id: number | string;
  first_name: string;
  last_name: string;
  image: string;
  role: string;
  username: string;
  email: string;
  posts_count: number;
  posts: {
    data: Post[],
    meta?: ApiResponseMeta;
  }
};


export interface IUser {
  avatar: string;
  description: string;
  dob: null;
  email: string;
  first_name: string;
  gender: string;
  id: number;
  last_name: string;
  name: string;
  phone: string;
}


export interface IAdmin extends IUser {
  roles: string[];
}

