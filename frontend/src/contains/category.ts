
import { TResponse } from '@/contains/post';
import { Route } from '@/routers/types';

export interface ICategory extends TResponse {
  data?: TCategory[];
}

export interface ICategoryDetail extends TResponse {
  data?: TCategory;
}


export type TCategory = {
  id: number;
  name: string;
  slug: Route;
  description: string;
  icon: string;
  image: string;
  posts_count: number;
  total_views: string;
  total_likes: number;
  total_comments: number;
  total_score: string;
  color: string;
}
