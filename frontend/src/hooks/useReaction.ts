'use client';
import { interaction<PERSON>pi } from '@/apis/interactionApi';
import { IReaction, IResponseReaction } from '@/contains/types';
import { toast } from 'react-toastify';

interface IUseReaction {
  postId: string | number;
  commentId?: string | number;
}

export const useReaction = ({ postId, commentId }: IUseReaction) => {

  const handleReaction = async () => {
    if (!postId) {
      return;
    }

    try {
      const res = await interactionApi.post<IResponseReaction>({
        endpoint: `posts/${postId}/toggle-like`
      });
      return res.data?.total_like;
    } catch (error) {
      console.error('Failed to toggle like:', error);
    }
  };

  const handleReactionCmt = async () => {
    if (!commentId) {
      return;
    }

    try {
      const res = await interactionApi.post<IResponseReaction>({
        endpoint: `comments/${commentId}/toggle-like`
      });
      return res.data?.total_like;
    } catch (error) {
      console.error('Failed to toggle like:', error);
    }
  };

  const handleGetReaction = async () => {
    if (!postId) {
      return '0';
    }

    try {
      const res = await interactionApi.get<IReaction>({
        endpoint: `posts/${postId}/count-like`
      });
      return res.data?.likes_count || '0';
    } catch (error) {
      console.error('Failed to get count like:', error);
    }
  };

  const handleGetReactionCmt = async () => {
    if (!postId) {
      return '0';
    }

    try {
      const res = await interactionApi.get<IReaction>({
        endpoint: `comments/${postId}/count-like`
      });
      return res.data?.likes_count || '0';
    } catch (error) {
      console.error('Failed to get count like:', error);
    }
  };

  return { handleReaction, handleGetReaction, handleReactionCmt, handleGetReactionCmt };
};

