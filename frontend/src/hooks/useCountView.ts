'use client';
import { useEffect, useRef } from 'react';
import { postApi } from '@/apis/postApi';

interface IUseCountView {
  postId: string | number;
}

export const useCountView = ({ postId }: IUseCountView) => {
  const hasCountedRef = useRef(false);

  useEffect(() => {
    if (!postId || hasCountedRef.current) {
      return;
    }

    const incrementView = async () => {
      try {
        await postApi.post({
          endpoint: `${postId}/increment-views`
        });
        hasCountedRef.current = true;
      } catch (error) {
        console.error('Failed to increment view count:', error);
      }
    };

    setTimeout(() => {
      incrementView().finally(() => {
      });
    }, 5000);
  }, [postId]);
};
