'use client';

import useS<PERSON> from 'swr';
import { IUser } from '@/contains/author';
import { KEY_SWR } from '@/contains/keySWR';

interface IUseUserServer {
  data: IUser | null;
  error: {message: string} | null;
  meta: boolean;
}

export function useUserServer(fallbackData?: IUseUserServer) {
  return useSWR<IUseUserServer>(
    KEY_SWR.GET_AUTH,
    null,
    {
      fallbackData
    }
  );
}
