import { IMenuItem, IThemeOptions } from '@/contains/types';
import { KEY_SWR } from '@/contains/keySWR';
import useSWR from 'swr';

export function useThemeOptions(fallbackData?: IThemeOptions) {
  return useSWR<IThemeOptions>(
    KEY_SWR.GET_THEME_OPTIONS,
    null,
    {
      fallbackData
    }
  );
}

export function useMenus(fallbackData?: IMenuItem[]) {
  return useSWR<IMenuItem[]>(
    KEY_SWR.GET_MENUS,
    null,
    {
      fallbackData
    }
  );
}
