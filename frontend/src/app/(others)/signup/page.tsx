'use client';

import React, { useState } from 'react';
import { SubmitHand<PERSON>, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { toast } from 'react-toastify';
import Image from 'next/image';

import Input from '@/components/Input/Input';
import ButtonPrimary from '@/components/Button/ButtonPrimary';
import NcLink from '@/components/NcLink/NcLink';
import Heading2 from '@/components/Heading/Heading2';

import { authApi } from '@/apis/authApi';
import { schemaRegisterUser, SignUpFormValues } from '@/data/validation';
import { Route } from '@/routers/types';
import eyeSvg from '@/images/eye.svg';
import eyeCloseSvg from '@/images/eyeClose.svg';
import Loading from '@/components/Button/Loading';
import { clientCreateCookie } from '@/lib/clientCookie';
import { forceReload } from '@/utils/forceReload';

const PageSignUp = () => {
  const [loading, setLoading] = useState<boolean>(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<SignUpFormValues>({
    resolver: yupResolver(schemaRegisterUser) as any,
    defaultValues: {
      email: '',
      name: '',
      phone: '',
      password: '',
      password_confirmation: ''
    }
  });

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const onSubmit: SubmitHandler<SignUpFormValues> = async (data) => {
    setLoading(true);
    try {
      const res = await authApi.post({
        endpoint: 'register',
        payload: JSON.stringify(data)
      });

      if (!res.error) {
        toast.success('Đăng ký thành công');
        reset();
        setTimeout(async () => {
          await clientCreateCookie(res.data.token).finally(() => {
            reset();
            forceReload();
          });
        }, 3000);
      } else {
        toast.error(res.error?.error?.message || 'Đăng ký không thành công');
      }
    } catch {
      toast.error('Đăng ký không thành công');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <header className="text-center max-w-2xl mx-auto mb-14 sm:mb-16 lg:mb-10">
        <Heading2>Đăng ký</Heading2>
        <span className="block text-sm mt-2 text-neutral-700 dark:text-neutral-200">
          Chào mừng đến với trang tin tức truyện tranh
        </span>
      </header>

      <div className="max-w-md mx-auto space-y-6">

        <form className="grid grid-cols-1 gap-6">
          <label className="block">
            <span className="text-neutral-800 dark:text-neutral-200">Email address</span>
            <Input type="email" {...register('email')} className="mt-1" placeholder="Nhập email" />
            {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>}
          </label>

          <label className="block">
            <span className="text-neutral-800 dark:text-neutral-200">Tên hiển thị</span>
            <Input type="text" {...register('name')} className="mt-1" placeholder="Nhập tên hiển thị" />
            {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>}
          </label>

          <label className="block">
            <span className="text-neutral-800 dark:text-neutral-200">Số điện thoại</span>
            <Input type="text" {...register('phone')} className="mt-1" placeholder="Nhập số điện thoại" />
            {errors.phone && <p className="text-red-500 text-sm mt-1">{errors.phone.message}</p>}
          </label>

          <label className="block relative">
            <span className="text-neutral-800 dark:text-neutral-200">Mật khẩu</span>
            <Input
              type={showPassword ? 'text' : 'password'}
              {...register('password')}
              className="mt-1 pr-10"
              placeholder="Nhập mật khẩu"
            />
            <span
              className="absolute right-3 top-[38px] cursor-pointer text-neutral-500 hover:text-neutral-800 dark:hover:text-white text-sm"
              onClick={() => setShowPassword(!showPassword)}
            >
              <Image src={showPassword ? eyeSvg : eyeCloseSvg} alt="toggle-password" />
            </span>
            {errors.password && <p className="text-red-500 text-sm mt-1">{errors.password.message}</p>}
          </label>

          <label className="block relative">
            <span className="text-neutral-800 dark:text-neutral-200">Nhập lại mật khẩu</span>
            <Input
              type={showConfirmPassword ? 'text' : 'password'}
              {...register('password_confirmation')}
              className="mt-1 pr-10"
              placeholder="Nhập lại mật khẩu"
            />
            <span
              className="absolute right-3 top-[38px] cursor-pointer text-neutral-500 hover:text-neutral-800 dark:hover:text-white text-sm"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            >
              <Image src={showConfirmPassword ? eyeSvg : eyeCloseSvg} alt="toggle-password" />
            </span>
            {errors.password_confirmation &&
              <p className="text-red-500 text-sm mt-1">{errors.password_confirmation.message}</p>}
          </label>
          <ButtonPrimary
            disabled={loading}
            type={'button'}
            onClick={() => handleSubmit(onSubmit)()}
          >
            Đăng Ký{loading && <span className="ml-4"><Loading /></span>}
          </ButtonPrimary>
        </form>

        <span className="block text-center text-neutral-700 dark:text-neutral-300">
          Bạn đã có tài khoản?{' '}
          <NcLink href={'/login' as Route}>Đăng nhập</NcLink>
        </span>
      </div>
    </>
  );
};

export default PageSignUp;
