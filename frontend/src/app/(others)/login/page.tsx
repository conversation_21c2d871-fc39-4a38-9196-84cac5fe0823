'use client';
import React, { useState } from 'react';
import { SubmitHand<PERSON>, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { toast } from 'react-toastify';
import Image from 'next/image';

import Input from '@/components/Input/Input';
import ButtonPrimary from '@/components/Button/ButtonPrimary';
import NcLink from '@/components/NcLink/NcLink';
import Heading2 from '@/components/Heading/Heading2';

import facebookSvg from '@/images/Facebook.svg';
import twitterSvg from '@/images/Twitter.svg';
import googleSvg from '@/images/Google.svg';
import eyeCloseSvg from '@/images/eyeClose.svg';
import eyeSvg from '@/images/eye.svg';

import { schemaLoginUser, SignInFormValues } from '@/data/validation';
import { authApi } from '@/apis/authApi';
import { Route } from '@/routers/types';
import { clientCreateCookie } from '@/lib/clientCookie';
import { forceReload } from '@/utils/forceReload';
import Loading from '@/components/Button/Loading';

const loginSocials = [
  { name: 'Continue with Facebook', href: '#', icon: facebookSvg },
  { name: 'Continue with Twitter', href: '#', icon: twitterSvg },
  { name: 'Continue with Google', href: '#', icon: googleSvg }
];

const PageLogin = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState<boolean>(false);

  const { register, handleSubmit, formState: { errors }, reset } = useForm<SignInFormValues>({
    resolver: yupResolver(schemaLoginUser)
  });

  const onSubmit: SubmitHandler<SignInFormValues> = async (data) => {
    setLoading(true);
    try {
      const res = await authApi.post({
        endpoint: 'login',
        payload: JSON.stringify(data)
      });

      if (res.data?.token) {
        await clientCreateCookie(res.data.token).finally(() => {
          reset();
          forceReload();
        });

      } else {
        toast.error(res.error?.error?.message || 'Đăng nhập thất bại');
      }
    } catch (error) {
      toast.error('Đăng nhập không thành công');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <header className="text-center max-w-2xl mx-auto mb-14 sm:mb-16 lg:mb-10">
        <Heading2>Đăng nhập</Heading2>
        <span className="block text-sm mt-2 text-neutral-700 dark:text-neutral-200">
          Chào mừng đến với trang tin tức truyện tranh
        </span>
      </header>

      <div className="max-w-md mx-auto space-y-6">
        {/* SOCIAL LOGIN */}
        {/*<div className="grid gap-3">*/}
        {/*  {loginSocials.map((item, index) => (*/}
        {/*    <a*/}
        {/*      key={index}*/}
        {/*      href={item.href}*/}
        {/*      className="flex w-full rounded-lg bg-primary-50 dark:bg-neutral-800 px-4 py-3 transition-transform hover:translate-y-[-2px]"*/}
        {/*    >*/}
        {/*      <Image className="flex-shrink-0" src={item.icon} alt={item.name} />*/}
        {/*      <h3 className="flex-grow text-center text-sm font-medium text-neutral-700 dark:text-neutral-300">{item.name}</h3>*/}
        {/*    </a>*/}
        {/*  ))}*/}
        {/*</div>*/}

        {/* OR LINE */}
        {/*<div className="relative text-center">*/}
        {/*  <span className="relative z-10 inline-block px-4 font-medium text-sm bg-white dark:bg-neutral-900 dark:text-neutral-400">OR</span>*/}
        {/*  <div className="absolute left-0 w-full top-1/2 transform -translate-y-1/2 border border-neutral-100 dark:border-neutral-800"></div>*/}
        {/*</div>*/}

        {/* FORM */}
        <form className="grid grid-cols-1 gap-6">
          {/* EMAIL */}
          <label>
            <span className="text-neutral-800 dark:text-neutral-200">Email</span>
            <Input
              type="email" {...register('email')} className="mt-1" disabled={loading}
            />
            {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>}
          </label>

          {/* PASSWORD */}
          <label className="relative">
            <span className="flex justify-between items-center text-neutral-800 dark:text-neutral-200">
              Mật khẩu
              <NcLink href={'/forgot-pass' as Route} className="text-sm underline">Quên Mật khẩu?</NcLink>
            </span>
            <Input
              type={showPassword ? 'text' : 'password'}
              {...register('password')}
              className="mt-1 pr-10"
              disabled={loading}
            />
            <span
              className="absolute right-3 top-[38px] cursor-pointer text-neutral-500 hover:text-neutral-800 dark:hover:text-white text-sm"
              onClick={() => setShowPassword(!showPassword)}
            >
              <Image src={showPassword ? eyeSvg : eyeCloseSvg} alt="toggle-password" />
            </span>
            {errors.password && <p className="text-red-500 text-sm mt-1">{errors.password.message}</p>}
          </label>
          <ButtonPrimary
            disabled={loading}
            type={'button'}
            onClick={() => handleSubmit(onSubmit)()}
          >
            Đăng nhập{loading && <span className="ml-4"><Loading /></span>}
          </ButtonPrimary>
        </form>

        {/* ==== */}
        <span className="block text-center text-neutral-700 dark:text-neutral-300">
          Bạn chưa có tài khoản? {` `}
          <NcLink href={'/signup' as Route}>Đăng ký ngay</NcLink>
        </span>

      </div>
    </>
  );
};

export default PageLogin;
