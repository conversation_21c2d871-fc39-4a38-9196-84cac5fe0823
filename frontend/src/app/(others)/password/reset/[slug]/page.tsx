'use client';

import React, { useState } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { toast } from 'react-toastify';
import Image from 'next/image';
import ButtonPrimary from '@/components/Button/ButtonPrimary';
import Input from '@/components/Input/Input';
import Loading from '@/components/Button/Loading';
import { authApi } from '@/apis/authApi';
import { ResetPassFormValues, schemaResetPass } from '@/data/validation';
import eyeCloseSvg from '@/images/eyeClose.svg';
import eyeSvg from '@/images/eye.svg';
import NcLink from '@/components/NcLink/NcLink';
import { Route } from '@/routers/types';
import Heading2 from '@/components/Heading/Heading2';
import { useParams, useSearchParams } from 'next/navigation';

const PageResetPass = () => {
  const params = useParams<{slug: string}>();
  const searchParams = useSearchParams();

  const email = searchParams.get('email') ?? '';
  const token = params.slug;
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<ResetPassFormValues>({
    resolver: yupResolver(schemaResetPass) as any,
    defaultValues: {
      email,
      password: '',
      password_confirmation: ''
    }
  });

  const onSubmit: SubmitHandler<ResetPassFormValues> = async (data) => {
    setLoading(true);
    try {
      const payload = { ...data, token }
      const res = await authApi.post({
        endpoint: 'reset-password',
        payload: JSON.stringify(payload)
      });

      if (!res.error) {
        toast.success('Khôi phục mật khẩu thành công');
      } else {
        toast.error('Khôi phục mật khẩu không thành công');
      }
    } catch {
      toast.error('Có lỗi xảy ra, vui lòng thử lại sau');
    } finally {
      reset();
      setLoading(false);
    }
  };

  return (
    <>
      <header className="text-center max-w-2xl mx-auto mb-14 sm:mb-16 lg:mb-10">
        <Heading2>Khôi Phục Mật Khẩu</Heading2>
        <span className="block text-sm mt-2 text-neutral-700 dark:text-neutral-200">
          Chào mừng đến với trang tin tức truyện tranh
        </span>
      </header>

      <div className="max-w-md mx-auto space-y-6">
        <form className="grid grid-cols-1 gap-6">
          {/* EMAIL */}
          <label>
            <span className="text-neutral-800 dark:text-neutral-200">Email</span>
            <Input
              type="email" {...register('email')} className="mt-1" disabled={loading}
            />
            {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>}
          </label>

          {/* PASSWORD */}
          <label className="relative">
            <span className="flex justify-between items-center text-neutral-800 dark:text-neutral-200">
              Mật khẩu
            </span>
            <Input
              type={showPassword ? 'text' : 'password'}
              {...register('password')}
              className="mt-1 pr-10"
              disabled={loading}
            />
            <span
              className="absolute right-3 top-[38px] cursor-pointer text-neutral-500 hover:text-neutral-800 dark:hover:text-white text-sm"
              onClick={() => setShowPassword(!showPassword)}
            >
              <Image src={showPassword ? eyeSvg : eyeCloseSvg} alt="toggle-password" />
            </span>
            {errors.password && <p className="text-red-500 text-sm mt-1">{errors.password.message}</p>}
          </label>

          <label className="relative">
            <span className="flex justify-between items-center text-neutral-800 dark:text-neutral-200">
              Mật khẩu
            </span>
            <Input
              type={showConfirmPassword ? 'text' : 'password'}
              {...register('password_confirmation')}
              className="mt-1 pr-10"
              disabled={loading}
            />
            <span
              className="absolute right-3 top-[38px] cursor-pointer text-neutral-500 hover:text-neutral-800 dark:hover:text-white text-sm"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            >
              <Image src={showConfirmPassword ? eyeSvg : eyeCloseSvg} alt="toggle-password" />
            </span>
            {errors.password_confirmation && <p className="text-red-500 text-sm mt-1">{errors.password_confirmation.message}</p>}
          </label>
          <ButtonPrimary
            disabled={loading}
            type={'button'}
            onClick={() => handleSubmit(onSubmit)()}
          >
            Khôi Phục{loading && <span className="ml-4"><Loading /></span>}
          </ButtonPrimary>
        </form>
        <span className="block text-center text-neutral-700 dark:text-neutral-300">
          Quay lại trang {` `}
          <NcLink href={'/login' as Route}>Đăng nhập</NcLink>
          {` / `}
          <NcLink href={'/signup' as Route}>Đăng ký</NcLink>
        </span>
      </div>
    </>
  );
};

export default PageResetPass;
