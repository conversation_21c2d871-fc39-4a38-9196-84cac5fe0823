import React, { ReactNode } from 'react';
import { Metadata } from 'next';
import { getThemeOption } from '@/lib/theme.server';
import DashboardContainer from '@/components/DashboardContainer';
import { handleConfigMetaSeo, handleConfigOpenGraph } from '@/lib/configMetaSeo';

export async function generateMetadata(): Promise<Metadata> {
  const themeData = await getThemeOption();
  const { site_title_separator = '-', site_title = '' } = themeData?.general || {};
  const separator = site_title_separator || '-';
  const siteTitle = site_title || 'Cuuduatin';
  const titlePage = `Cập nhật mật khẩu ${separator} ${siteTitle}`;

  const seoIndex = themeData?.general?.seo_index;
  const { canonical } = handleConfigMetaSeo({
    indexFollow: !!seoIndex ? 'index' : '',
    type: ''
  });
  const descriptionPage = themeData?.general.seo_description || '';
  const imageUrl = themeData?.general.seo_og_image || '';
  const { twitter, openGraph } = handleConfigOpenGraph({
    themeData,
    titlePage,
    descriptionPage,
    canonical,
    siteTitle,
    altImage: 'Cập nhật mật khẩu',
    imageUrl
  });

  return {
    title: titlePage,
    twitter,
    openGraph
  };
}

const layout = ({ children }: {children: ReactNode}) => {
  return (
    <div className={`relative`}>
      <DashboardContainer>
        {children}
      </DashboardContainer>
    </div>
  );
};

export default layout;
