'use client';

import React, { useState } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { toast } from 'react-toastify';
import Image from 'next/image';
import ButtonPrimary from '@/components/Button/ButtonPrimary';
import Input from '@/components/Input/Input';
import Loading from '@/components/Button/Loading';
import { authApi } from '@/apis/authApi';
import { ChangePassFormValues, schemaChangePass } from '@/data/validation';
import eyeCloseSvg from '@/images/eyeClose.svg';
import eyeSvg from '@/images/eye.svg';

const DashboardEditPassword = () => {
  const [showOldPassword, setShowOldPassword] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<ChangePassFormValues>({
    resolver: yupResolver(schemaChangePass) as any,
    defaultValues: {
      current_password: '',
      password: ''
    }
  });

  const onSubmit: SubmitHandler<ChangePassFormValues> = async (data) => {
    setLoading(true);
    try {
      const res = await authApi.post({
        endpoint: 'update/password',
        payload: JSON.stringify(data)
      });

      if (!res.error) {
        toast.success('Đổi mật khẩu thành công');
      } else {
        toast.error(res.error?.error?.message || 'Sai mật khẩu cũ');
      }
    } catch {
      toast.error('Đổi mật khẩu không thành công');
    } finally {
      reset();
      setLoading(false);
    }
  };

  return (
    <div className="rounded-xl md:border md:border-neutral-100 dark:border-neutral-800 md:p-6">
      <form className="grid md:grid-cols-2 gap-6">

        <label className="block relative">
          <span className="text-neutral-800 dark:text-neutral-200">Mật khẩu cũ</span>
          <Input
            type={showOldPassword ? 'text' : 'password'}
            {...register('current_password')}
            className="mt-1 pr-10"
            placeholder="Nhập mật khẩu cũ"
            disabled={loading}
          />
          <span
            className="absolute right-3 top-[38px] cursor-pointer text-neutral-500 hover:text-neutral-800 dark:hover:text-white text-sm"
            onClick={() => setShowOldPassword(!showOldPassword)}
          >
            <Image src={showOldPassword ? eyeSvg : eyeCloseSvg} alt="toggle-password" />
          </span>
          {errors.current_password && <p className="text-red-500 text-sm mt-1">{errors.current_password.message}</p>}
        </label>

        <label className="block relative">
          <span className="text-neutral-800 dark:text-neutral-200">Mật khẩu mới</span>
          <Input
            type={showPassword ? 'text' : 'password'}
            {...register('password')}
            className="mt-1 pr-10"
            placeholder="Nhập mật khẩu mới"
            disabled={loading}
          />
          <span
            className="absolute right-3 top-[38px] cursor-pointer text-neutral-500 hover:text-neutral-800 dark:hover:text-white text-sm"
            onClick={() => setShowPassword(!showPassword)}
          >
            <Image src={showPassword ? eyeSvg : eyeCloseSvg} alt="toggle-password" />
          </span>
          {errors.password && <p className="text-red-500 text-sm mt-1">{errors.password.message}</p>}
        </label>

        <ButtonPrimary
          className="md:col-span-2"
          disabled={loading}
          type={'button'}
          onClick={() => handleSubmit(onSubmit)()}
        >
          Đổi mật khẩu
          {loading && <span className="ml-4"><Loading /></span>}
        </ButtonPrimary>
      </form>
    </div>
  );
};

export default DashboardEditPassword;
