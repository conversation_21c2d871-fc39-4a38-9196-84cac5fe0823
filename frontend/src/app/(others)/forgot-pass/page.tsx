'use client';
import React, { useState } from 'react';
import Input from '@/components/Input/Input';
import ButtonPrimary from '@/components/Button/ButtonPrimary';
import NcLink from '@/components/NcLink/NcLink';
import Heading2 from '@/components/Heading/Heading2';
import { Route } from '@/routers/types';
import { SubmitHandler, useForm } from 'react-hook-form';
import { ForgotPasswordFormValues, schemaForgotPassword } from '@/data/validation';
import { yupResolver } from '@hookform/resolvers/yup';
import { authApi } from '@/apis/authApi';
import { toast } from 'react-toastify';
import Loading from '@/components/Button/Loading';

const PageForgotPass = ({}) => {
  const [loading, setLoading] = useState<boolean>(false);

  const { register, handleSubmit, formState: { errors }, reset } = useForm<ForgotPasswordFormValues>({
    resolver: yupResolver(schemaForgotPassword)
  });

  const onSubmit: SubmitHandler<ForgotPasswordFormValues> = async (data) => {
    setLoading(true);
    try {
      const res = await authApi.post({
        endpoint: 'forgot-password',
        payload: JSON.stringify(data)
      });
      if (!res.error) {
        toast.success(`Chúng tôi đã gửi email xác thực đến ${data.email}`);
      } else {
        toast.error(`Email ${data.email} không tồn tại`);
      }
    } catch (error) {
      toast.error('Có lỗi xảy ra, vui lòng thử lại sau');
    } finally {
      setLoading(false);
    }
  };
  return (
    <>
      <header className="text-center max-w-2xl mx-auto - mb-14 sm:mb-16 lg:mb-10">
        <Heading2>Quên mật khẩu</Heading2>
        <span className="block text-sm mt-2 text-neutral-700 dark:text-neutral-200">
          Chào mừng đến với trang tin tức truyện tranh
        </span>
      </header>

      <div className="max-w-md mx-auto space-y-6">
        {/* FORM */}
        <form className="grid grid-cols-1 gap-6" action="#" method="post">
          <label>
            <span className="text-neutral-800 dark:text-neutral-200">Email</span>
            <Input
              type="email" {...register('email')} className="mt-1" disabled={loading}
            />
            {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>}
          </label>
          <ButtonPrimary
            disabled={loading}
            type={'button'}
            onClick={() => handleSubmit(onSubmit)()}
          >
            Quên mật khẩu{loading && <span className="ml-4"><Loading /></span>}
          </ButtonPrimary>
        </form>

        {/* ==== */}
        <span className="block text-center text-neutral-700 dark:text-neutral-300">
          Quay lại trang {` `}
          <NcLink href={'/login' as Route}>Đăng nhập</NcLink>
          {` / `}
          <NcLink href={'/signup' as Route}>Đăng ký</NcLink>
        </span>
      </div>
    </>
  );
};

export default PageForgotPass;
