import React from 'react';
import Pagination from '@/components/Pagination/Pagination';
import Card11 from '@/components/Card11/Card11';
import { Metadata } from 'next';
import { tagApi } from '@/apis/tagApi';
import { ITag, ITagDetail } from '@/contains/tag';
import { ApiResponseMeta, IPost } from '@/contains/post';
import { postApi } from '@/apis/postApi';
import { ITEM_PER_PAGE_16 } from '@/utils/constant';
import ModalTags from '@/app/(tag)/ModalTags';
import Tag from '@/components/Tag/Tag';
import { ENDPOINT } from '@/contains/endpoint';
import { metaSeoApi } from '@/apis/metaSeoApi';
import { IMetaSeo } from '@/contains/types';
import { handleConfigMetaSeo, handleConfigOpenGraph } from '@/lib/configMetaSeo';
import { HeaderSection } from '@/components/HeaderSection';
import { MainSection } from '@/components/MainSection';
import { getThemeOption } from '@/lib/theme.server';
import Nodata from '@/components/Nodata';
import { getToken } from '@/lib/cookie';

interface PostByTagProps {
  params: {slug: string[]},
  searchParams?: {[key: string]: string | undefined};
}

const getTagList = async (options: {page: number, per_page: number, order: string, order_by: string}) => {
  const { page, per_page, order, order_by } = options;
  const res = await tagApi.get<ITag>({
    endpoint: ENDPOINT.GET_TAGS_FILTERS_WITH_SCORE,
    params: {
      page, per_page,
      ...( order && { order } ),
      ...( order_by && { order_by } )
    }
  }) as ITag;

  return {
    tagData: res?.data ?? [],
    totalCount: res?.meta?.total ?? 0
  };
};

const getTagDetail = async (slug: string) => {
  const res = await tagApi.get<ITagDetail>({ endpoint: slug }) as ITagDetail;
  return res?.data;
};

const getPostsByTag = async (tagId: number, options: {
  page: number,
  per_page: number,
  order: string,
  order_by: string
}) => {
  const { page, per_page, order, order_by } = options;
  const cookieToken = getToken();
  const res = await postApi.get<IPost>({
    endpoint: ENDPOINT.GET_POSTS_FILTERS_SCORE,
    params: {
      'tags[]': tagId,
      page,
      per_page,
      ...( order && { order } ),
      ...( order_by && { order_by } )
    },
    headers: cookieToken ? { Authorization: `Bearer ${cookieToken}` } : undefined
  }) as IPost;

  return {
    postData: res?.data ?? [],
    meta: res?.meta as ApiResponseMeta
  };
};

export async function generateMetadata(
  { params }: PostByTagProps
): Promise<Metadata> {
  const slug = params.slug?.[0] ?? '';

  const [themeData, tagDetail, tagMetaSeo] = await Promise.all([
    getThemeOption(),
    getTagDetail(slug),
    metaSeoApi.get<IMetaSeo>({
      params: {
        slug,
        model: 'category',
        lang: 'en'
      }
    })
  ]);
  const { site_title_separator = '-', site_title = '' } = themeData?.general || {};
  const separator = site_title_separator || '-';
  const siteTitle = site_title || 'Cuuduatin';

  const tagMetaSeoDetail = tagMetaSeo?.data;
  const titlePage = `Thẻ ${tagMetaSeoDetail?.meta_value_custom?.seo_title || tagDetail?.name || ''} ${separator} ${siteTitle}`;
  const descriptionPage = `${tagMetaSeoDetail?.meta_value_custom?.seo_description || tagDetail?.description || 'Danh sách thẻ bài viết'}`;
  const { robots, canonical } = handleConfigMetaSeo({
    slug: slug ?? '',
    indexFollow: tagMetaSeoDetail?.meta_value_custom?.index ?? '',
    type: 'tag'
  });

  const isRootSlug = slug === '';

  const imageUrl = themeData?.general.seo_og_image || '';

  const { twitter, openGraph } = handleConfigOpenGraph({
    themeData,
    titlePage,
    descriptionPage,
    canonical,
    siteTitle,
    altImage: tagDetail?.name || '',
    imageUrl
  });

  return {
    title: isRootSlug
      ? `Danh sách Thẻ bài viết ${separator} ${siteTitle}`
      : titlePage,
    description: isRootSlug
      ? 'Danh sách Thẻ bài viết'
      : descriptionPage,
    alternates: {
      canonical
    },
    other: robots,
    openGraph,
    twitter
  };
}

export default async function Page({ searchParams, params }: PostByTagProps) {
  const themeData = await getThemeOption();

  const slug = params.slug?.[0] ?? '';
  const page = parseInt(searchParams?.page || '1');
  const per_page = parseInt(themeData?.blog.number_of_posts_in_a_tag ?? `${ITEM_PER_PAGE_16}`);
  const order_by = searchParams?.order_by || '';
  const order = searchParams?.order || '';

  if (!slug) {
    const { tagData, totalCount } = await getTagList({ page, per_page, order, order_by });

    return (
      <div className="nc-PostByTag">
        <div className="pt-16">
          <HeaderSection title="Thẻ bài viết" subTitle="Thẻ" count={totalCount} />
        </div>
        <MainSection modal={<ModalTags />}>
          <div className="grid sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 md:gap-8 mt-8 lg:mt-10">
            {tagData.map(tag => (
              <Tag key={tag.id} tag={tag} className="me-2 mb-2" />
            ))}
          </div>
          {tagData.length === 0 && <Nodata />}
          <div className="py-16">
            <Pagination perPage={per_page} total={totalCount} currentPage={page} />
          </div>
        </MainSection>
      </div>
    );
  }

  const tag = await getTagDetail(slug);
  const tagName = tag?.name || '';
  const tagId = tag?.id;

  if (!tagId) {
    return (
      <div className="py-16">
        <HeaderSection title="Thẻ không tồn tại" subTitle={''} count={0} />
      </div>
    );
  }

  const { postData, meta } = await getPostsByTag(tagId, { page, per_page, order, order_by });

  return (
    <div className="nc-PostByTag">
      <div className="pt-16">
        <HeaderSection title={`Thẻ: ${tagName}`} subTitle={'bài viết'} count={meta.total} />
      </div>
      <MainSection modal={<ModalTags />}>
        <div className="grid sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 md:gap-8 mt-8 lg:mt-10">
          {postData.map(post => (
            <Card11 key={post.id} post={post} />
          ))}
        </div>
        {postData.length === 0 && <Nodata />}
        <div className="py-16">
          <Pagination perPage={per_page} total={meta.total} currentPage={page} />
        </div>
      </MainSection>
    </div>
  );
}
