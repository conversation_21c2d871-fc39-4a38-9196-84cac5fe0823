'use client';

import React, { FC, useEffect, useRef, useState } from 'react';
import NcModal from '@/components/NcModal/NcModal';
import Tag from '@/components/Tag/Tag';
import Button from '@/components/Button/Button';
import { ChevronDownIcon } from '@heroicons/react/24/solid';
import { ITag, TTag } from '@/contains/tag';
import { tagApi } from '@/apis/tagApi';
import { ITEM_PER_PAGE_20 } from '@/utils/constant';

const getListTags = async (page: number) => {
  const tagResponse: ITag = await tagApi.get<ITag>({
    params: {
      page,
      per_page: ITEM_PER_PAGE_20
    }
  }) as ITag;
  return tagResponse.data;
};

const ModalTags: FC = () => {
  const [tags, setTags] = useState<TTag[]>([]);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [loading, setLoading] = useState<boolean>(false);
  const containerRef = useRef<HTMLDivElement>(null);


  useEffect(() => {
    getListTags(1).then((res) => {
      setTags(res ?? []);
    });
  }, []);

  const loadMoreTags = async () => {
    if (loading || !hasMore) {
      return;
    }

    setLoading(true);
    const nextPage = currentPage + 1;
    try {
      const newTags = await getListTags(nextPage);
      if (newTags && newTags.length > 0) {
        setTags(prevTags => [...prevTags, ...newTags]);
        setCurrentPage(nextPage);
      } else {
        setHasMore(false);
      }
    } catch (error) {
      console.error('Error loading more tags:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleScroll = () => {
    if (!containerRef.current) {
      return;
    }

    const { scrollTop, scrollHeight, clientHeight } = containerRef.current;
    if (scrollTop + clientHeight >= scrollHeight - 20 && hasMore && !loading) {
      loadMoreTags().finally();
    }
  };

  const renderModalContent = () => {
    return (
      <div
        className="flex flex-wrap dark:text-neutral-200 max-h-[50vh] overflow-y-auto p-2"
        ref={containerRef}
        onScroll={handleScroll}
      >
        {tags.map((tag) => (
          <Tag key={tag.id} tag={tag} className="me-2 mb-2" />
        ))}
        {loading && <div className="w-full text-center py-3">Đang tải...</div>}
        {!hasMore && tags.length > 0 && <div className="w-full text-center py-3">-- Hết --</div>}
      </div>
    );
  };

  return (
    <div className="nc-ModalTags">
      <NcModal
        contentExtraClass="max-w-screen-md"
        renderTrigger={(openModal) => (
          <Button
            pattern="third"
            fontSize="text-sm font-medium"
            onClick={openModal}
          >
            <div>
              <span className="hidden sm:inline">Các thẻ</span> khác
            </div>
            <ChevronDownIcon
              className="w-4 h-4 ms-2 -me-1"
              aria-hidden="true"
            />
          </Button>
        )}
        modalTitle="Khám phá các thẻ khác"
        renderContent={renderModalContent}
      />
    </div>
  );
};

export default ModalTags;
