import React, { lazy, Suspense } from 'react';
import LoadingEffect from '../../components/LoadingEffect';
import { IWidget, IWidgetsResponse } from '@/contains/widget';
import { widgetsApi } from '@/apis/widgetsApi';
import DefaultLayout from '@/components/DefaultLayout';

const SectionAds = lazy(() => import('@/components/Sections/SectionAds'));
const SectionBecomeAnAuthor = lazy(() => import('@/components/SectionBecomeAnAuthor/SectionBecomeAnAuthor'));
const CategoryTrendingWarper = lazy(() => import('@/components/Home/CategoryTrendingWarper').then(m => ( { default: m.CategoryTrendingWarper } )));
const BannerWarper = lazy(() => import('@/components/Home/BannerWarper').then(m => ( { default: m.BannerWarper } )));
const AuthorWarper = lazy(() => import('@/components/Home/AuthorWarper').then(m => ( { default: m.AuthorWarper } )));
const MostCommentWarper = lazy(() => import('@/components/Home/MostCommentWarper').then(m => ( { default: m.MostCommentWarper } )));
const MostScoreWarper = lazy(() => import('@/components/Home/MostScoreWarper').then(m => ( { default: m.MostScoreWarper } )));
const MostFeaturedCategoriesWarper = lazy(() => import('@/components/Home/MostFeaturedCategoriesWarper').then(m => ( { default: m.MostFeaturedCategoriesWarper } )));
const NewestPostWarper = lazy(() => import('@/components/Home/NewestPostWarper').then(m => ( { default: m.NewestPostWarper } )));
const GridPostWarper = lazy(() => import('@/components/Home/GridPostWarper').then(m => ( { default: m.GridPostWarper } )));

interface IPageHomeProps {
  searchParams: {[key: string]: string | undefined};
}

const getWidgets = async () => {
  const res: IWidgetsResponse = await widgetsApi.get<IWidgetsResponse>() as IWidgetsResponse;
  return handleSortWidgets(res.data) || [];
};

const handleSortWidgets = (widgets?: IWidget[]) => {
  if (!widgets) {
    return [];
  }
  return widgets.filter((widget) => widget.status === 'active').sort((a, b) => a.position - b.position);
};

const PageHome = async ({ searchParams }: IPageHomeProps) => {
  const widgetData = await getWidgets();

  // default layout when widget api is not available
  if (!widgetData.length) {
    return <DefaultLayout searchParams={searchParams}/>;
  }

  return (
    <div className="nc-PageHome relative">
      {widgetData.map((item) => {
        switch (item.widget) {
          case 'banner':
            return (
              <Suspense fallback={<LoadingEffect />} key={`${item.name}-${item.position}`}>
                <BannerWarper heading={item.name} />
              </Suspense>
            );
          case 'latest_author_list':
            return (
              <Suspense fallback={<LoadingEffect />} key={`${item.name}-${item.position}`}>
                <AuthorWarper heading={item.name} />
              </Suspense>
            );
          case 'featured_categories':
            return (
              <Suspense fallback={<LoadingEffect />} key={`${item.name}-${item.position}`}>
                <CategoryTrendingWarper heading={item.name} />
              </Suspense>
            );
          case 'most_featured_posts_in_each_category':
            return (
              <Suspense fallback={<LoadingEffect />} key={`${item.name}-${item.position}`}>
                <MostCommentWarper heading={item.name} />
              </Suspense>
            );
          case 'ads':
            return (
              <Suspense fallback={<LoadingEffect />} key={`${item.name}-${item.position}`}>
                <SectionAds className="py-16 lg:py-28" />
              </Suspense>
            );

          case 'explore_latest_posts':
            return (
              <Suspense fallback={<LoadingEffect />} key={`${item.name}-${item.position}`}>
                <GridPostWarper heading={item.name} />
              </Suspense>
            );

          case 'become_an_author':
            return (
              <Suspense fallback={<LoadingEffect />} key={`${item.name}-${item.position}`}>
                <SectionBecomeAnAuthor heading={item.name}/>
              </Suspense>
            );

          case 'featured_categories_list':
            return (
              <Suspense fallback={<LoadingEffect />} key={`${item.name}-${item.position}`}>
                <MostFeaturedCategoriesWarper heading={item.name} />
              </Suspense>
            );
          case 'slider_of_posts_by_latest_tag':
            return (
              <Suspense fallback={<LoadingEffect />} key={`${item.name}-${item.position}`}>
                <MostScoreWarper heading={item.name} />
              </Suspense>
            );
          case 'latest_posts_with_widget':
            return (
              <Suspense fallback={<LoadingEffect />} key={`${item.name}-${item.position}`}>
                <NewestPostWarper searchParams={searchParams} className="py-16 lg:py-28" />
              </Suspense>
            );
          default:
            return <></>;
        }
      })}
    </div>
  );
};

export default PageHome;
