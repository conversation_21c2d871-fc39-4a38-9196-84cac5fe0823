import { Suspense } from 'react';
import LoadingEffect from '../../../components/LoadingEffect';
import SearchPage from '@/components/SearchPage';
import { Metadata } from 'next';
import { handleConfigMetaSeo, handleConfigOpenGraph } from '@/lib/configMetaSeo';
import { getThemeOption } from '@/lib/theme.server';

interface ISearchPage {
  params: {
    searchParams?: {[key: string]: string | undefined};
  },
}

export async function generateMetadata(
  { params }: ISearchPage
): Promise<Metadata> {
  const { searchParams } = params;

  const theme = await getThemeOption();

  const { seo_title, site_title_separator, site_description, seo_description, seo_index } = theme?.general || {};
  const separator = site_title_separator || '-';
  const siteTitle = seo_title || 'Cuuduatin';

  const { robots, canonical } = handleConfigMetaSeo({
    indexFollow: !!seo_index ? 'index' : '',
    type: 'search'
  });
  const titlePage = `Trang tìm kiếm ${searchParams?` "${searchParams}" `:' '}${separator} ${siteTitle}`;
  const descriptionPage = seo_description || site_description || seo_title || '';
  const imageUrl = theme?.general.seo_og_image || '';

  const { twitter, openGraph } = handleConfigOpenGraph({
    themeData:theme,
    titlePage,
    descriptionPage,
    canonical,
    siteTitle,
    altImage: '',
    imageUrl
  });

  return {
    title: titlePage,
    description: descriptionPage,
    alternates: {
      canonical
    },
    other: robots,
    twitter,
    openGraph
  };
}

export default function SearchPageWrapper() {
  return (
    <Suspense fallback={<LoadingEffect />}>
      <SearchPage />
    </Suspense>
  );
}
