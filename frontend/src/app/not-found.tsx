import ButtonPrimary from "@/components/Button/ButtonPrimary";
import React from "react";
import { Metadata } from 'next';
import { getThemeOption } from '@/lib/theme.server';

export async function generateMetadata(): Promise<Metadata> {
  const themeData = await getThemeOption()
  const { site_title_separator = '-', site_title = '' } = themeData?.general || {};
  const separator = site_title_separator || '-';
  const siteTitle = site_title || 'Cuuduatin';
  const titlePage = `Không tìm thấy trang ${separator} ${siteTitle}`;

  return {
    title: titlePage,
  };
}

const Page404: React.FC = () => (
  <div className="nc-Page404">
    <div className="container relative py-16 lg:py-20">
      {/* HEADER */}
      <header className="text-center max-w-2xl mx-auto space-y-7">
        <h2 className="text-7xl md:text-8xl">🪔</h2>
        <h1 className="text-8xl md:text-9xl font-semibold tracking-widest">
          404
        </h1>
        <span className="block text-sm text-neutral-800 sm:text-base dark:text-neutral-200 tracking-wider font-medium">
          {`Trang bạn đang tìm kiếm không tồn tại.`}
        </span>
        <ButtonPrimary href="/" className="mt-4">
          Trở về trang chủ
        </ButtonPrimary>
      </header>
    </div>
  </div>
);

export default Page404;
