'use client';

import React, { useEffect, useRef, useState } from 'react';
import NcModal from '@/components/NcModal/NcModal';
import Button from '@/components/Button/Button';
import { ChevronDownIcon } from '@heroicons/react/24/solid';
import { ICategory, TCategory } from '@/contains/category';
import { ITEM_PER_PAGE_20 } from '@/utils/constant';
import { categoryApi } from '@/apis/categoryApi';
import CardCategory1 from '@/components/CardCategory1/CardCategory1';

const getListCategories = async (page: number) => {
  const categoryResponse: ICategory = await categoryApi.get<ICategory>({
    params: {
      page,
      per_page: ITEM_PER_PAGE_20
    }
  }) as ICategory;
  return categoryResponse.data;
};
const ModalCategories = () => {
  const [categories, setCategories] = useState<TCategory[]>([]);

  const [currentPage, setCurrentPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [loading, setLoading] = useState<boolean>(false);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    getListCategories(1).then((res) => {
      setCategories(res ?? []);
    });
  }, []);

  const loadMoreCategories = async () => {
    if (loading || !hasMore) {
      return;
    }

    setLoading(true);
    const nextPage = currentPage + 1;
    try {
      const newCategories = await getListCategories(nextPage);
      if (newCategories && newCategories.length > 0) {
        setCategories(prevCate => [...prevCate, ...newCategories]);
        setCurrentPage(nextPage);
      } else {
        setHasMore(false);
      }
    } catch (error) {
      console.error('Error loading more categories:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleScroll = () => {
    if (!containerRef.current) {
      return;
    }

    const { scrollTop, scrollHeight, clientHeight } = containerRef.current;
    if (scrollTop + clientHeight >= scrollHeight - 20 && hasMore && !loading) {
      loadMoreCategories().finally();
    }
  };

  const renderModalContent = () => {
    return (
      <div
        className="grid gap-6 sm:grid-cols-2 sm:py-2 md:gap-8 md:grid-cols-3 lg:grid-cols-4 xl:md:grid-cols-5"
        ref={containerRef}
        onScroll={handleScroll}
      >
        {categories.map((cat) => (
          <CardCategory1 key={cat.id} categories={cat} className="me-2 mb-2"/>
        ))}
        {loading && <div className="w-full text-center py-3">Đang tải...</div>}
        {!hasMore && categories.length > 0 && <div className="w-full text-center py-3">-- Hết --</div>}
      </div>
    );
  };

  return (
    <div className="nc-ModalCategories">
      <NcModal
        renderTrigger={(openModal) => (
          <Button
            pattern="third"
            fontSize="text-sm font-medium"
            onClick={openModal}
          >
            <div>
              <span className="hidden sm:inline">Danh mục</span> khác
            </div>
            <ChevronDownIcon
              className="w-4 h-4 ms-2 -me-1"
              aria-hidden="true"
            />
          </Button>
        )}
        modalTitle="Khám phá các danh mục khác"
        renderContent={renderModalContent}
      />
    </div>
  );
};

export default ModalCategories;
