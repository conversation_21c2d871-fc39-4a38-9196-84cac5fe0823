import React from 'react';
import ModalCategories from '../../ModalCategories';
import Pagination from '@/components/Pagination/Pagination';
import Card11 from '@/components/Card11/Card11';
import { Metadata } from 'next';
import CardCategory2 from '@/components/CardCategory2/CardCategory2';

import { ICategory, ICategoryDetail } from '@/contains/category';
import { categoryApi } from '@/apis/categoryApi';
import { ITEM_PER_PAGE_16 } from '@/utils/constant';
import { ApiResponseMeta, IPost } from '@/contains/post';
import { postApi } from '@/apis/postApi';
import { ENDPOINT } from '@/contains/endpoint';
import { metaSeoApi } from '@/apis/metaSeoApi';
import { IMetaSeo } from '@/contains/types';
import { handleConfigMetaSeo, handleConfigOpenGraph } from '@/lib/configMetaSeo';
import { getThemeOption } from '@/lib/theme.server';
import { MainSection } from '@/components/MainSection';
import { HeaderSection } from '@/components/HeaderSection';
import Nodata from '@/components/Nodata';
import { getToken } from '@/lib/cookie';

interface PostByCategoryProps {
  params: {slug: string[]},
  searchParams?: {[key: string]: string | undefined};
}

const getCategoryList = async (options: {page: number, per_page: number, order: string, order_by: string}) => {
  const { page, per_page, order, order_by } = options;
  const res = await categoryApi.get<ICategory>({
    endpoint: ENDPOINT.GET_CATEGORIES_FILTERS_WITH_SCORE,
    params: {
      page, per_page,
      ...( order && { order } ),
      ...( order_by && { order_by } )
    }
  }) as ICategory;

  return {
    categoryData: res?.data ?? [],
    totalCount: res?.meta?.total ?? 0
  };
};

const getCategoryDetail = async (slug: string) => {
  const res = await categoryApi.getCustom<ICategoryDetail>({ endpoint: slug }) as ICategoryDetail;
  return res?.data;
};

const getPostsByCategory = async (categoryId: number, options: {
  page: number,
  per_page: number,
  order: string,
  order_by: string
}) => {
  const { page, per_page, order, order_by } = options;
  const cookieToken = getToken();
  const res = await postApi.get<IPost>({
    endpoint: ENDPOINT.GET_POSTS_FILTERS_SCORE,
    params: {
      'categories[]': categoryId,
      page,
      per_page,
      ...( order && { order } ),
      ...( order_by && { order_by } )
    },
    headers: cookieToken ? { Authorization: `Bearer ${cookieToken}` } : undefined
  }) as IPost;

  return {
    postData: res?.data ?? [],
    meta: res?.meta as ApiResponseMeta
  };
};

export async function generateMetadata(
  { params }: PostByCategoryProps
): Promise<Metadata> {
  const slug = params.slug?.[0] ?? '';

  const [themeData, cateDetail, cateMetaSeo] = await Promise.all([
    getThemeOption(),
    getCategoryDetail(slug),
    metaSeoApi.get<IMetaSeo>({
      params: {
        slug,
        model: 'category',
        lang: 'en'
      }
    })
  ]);

  const { site_title_separator = '-', site_title = '' } = themeData?.general || {};
  const separator = site_title_separator || '-';
  const siteTitle = site_title || 'Cuuduatin';

  const cateMetaSeoDetail = cateMetaSeo?.data;
  const titlePage = `Danh mục ${cateMetaSeoDetail?.meta_value_custom?.seo_title || cateDetail?.name || ''} ${separator} ${siteTitle}`;
  const descriptionPage = `${cateMetaSeoDetail?.meta_value_custom?.seo_description || cateDetail?.description || 'Danh sách danh mục bài viết'}`;
  const { robots, canonical } = handleConfigMetaSeo({
    slug: slug ?? '',
    indexFollow: cateMetaSeoDetail?.meta_value_custom?.index ?? '',
    type: 'category'
  });

  const isRootSlug = slug === '';

  const imageUrl = cateDetail?.image || themeData?.general.seo_og_image || '';

  const { twitter, openGraph } = handleConfigOpenGraph({
    themeData,
    titlePage,
    descriptionPage,
    canonical,
    siteTitle,
    altImage: cateDetail?.name || '',
    imageUrl
  });
  return {
    title: isRootSlug
      ? `Danh sách danh mục bài viết ${separator} ${siteTitle}`
      : titlePage,
    description: isRootSlug
      ? 'Danh sách danh mục bài viết'
      : descriptionPage,
    alternates: {
      canonical
    },
    other: robots,
    openGraph,
    twitter
  };
}

export default async function Page({ searchParams, params }: PostByCategoryProps) {
  const themeData = await getThemeOption();

  const slug = params.slug?.[0] ?? '';
  const page = parseInt(searchParams?.page || '1');
  const per_page = parseInt(themeData?.blog.number_of_posts_in_a_category ?? `${ITEM_PER_PAGE_16}`);
  const order_by = searchParams?.order_by || '';
  const order = searchParams?.order || '';

  if (!slug) {
    const { categoryData, totalCount } = await getCategoryList({ page, per_page, order, order_by });

    return (
      <div className="nc-PageArchive">
        <div className="pt-16">
          <HeaderSection title="Danh mục bài viết" subTitle="Danh mục" count={totalCount} />
        </div>
        <MainSection modal={<ModalCategories />}>
          <div className="grid sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 md:gap-8 mt-8 lg:mt-10">
            {categoryData.map((cate) => (
              <CardCategory2 key={cate.id} category={cate} />
            ))}
          </div>
          {categoryData.length === 0 && <Nodata />}
          <div className="py-16">
            <Pagination perPage={per_page} total={totalCount} currentPage={page} />
          </div>
        </MainSection>
      </div>
    );
  }

  const category = await getCategoryDetail(slug);
  const categoryName = category?.name || '';
  const categoryId = category?.id;
  const imageBanner = category?.image || '';

  if (!categoryId) {
    return (
      <div className="py-16">
        <HeaderSection title="Danh mục không tồn tại" subTitle={''} count={0} />
      </div>
    );
  }

  const { meta = { total: 0 }, postData = [] } = categoryId
    ? await getPostsByCategory(categoryId, { page, per_page, order, order_by })
    : {};

  const totalCount = meta?.total ?? 0;

  return (
    <div className="nc-PageArchive">
      <div className="pt-16">
        <HeaderSection title={`Danh Mục: ${categoryName}`} subTitle={'bài viết'} count={totalCount} image={imageBanner}/>
      </div>
      <MainSection modal={<ModalCategories />}>
        <div className="grid sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 md:gap-8 mt-8 lg:mt-10">
          {postData.map((post) => (
            <Card11 key={post.id} post={post} />
          ))}
        </div>
        {postData.length === 0 && <Nodata />}
        <div className="py-16">
          <Pagination perPage={per_page} total={totalCount} currentPage={page} />
        </div>
      </MainSection>
    </div>
  );
}
