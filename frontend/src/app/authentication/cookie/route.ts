import { clearCookie, create<PERSON><PERSON><PERSON> } from '@/lib/cookie';

export async function POST(request: Request) {
  const { token } = await request.json();

  if (!token) {
    return new Response('Missing token', { status: 400 });
  }

  createCookie(token);
  return new Response('cookie created', { status: 200 });
}

export async function DELETE() {
  clearCookie();
  return new Response('cookie cleared', { status: 200 });
}
