'use client';

import PostActionDropdown from '@/components/PostActionDropdown/PostActionDropdown';
import PostCardLikeAndComment from '@/components/PostCardLikeAndComment/PostCardLikeAndComment';
import React, { FC } from 'react';
import { Post } from '@/contains/post';

export interface SingleMetaAction2Props {
  className?: string;
  post: Post;
  comments_count?: number;
}

const SingleMetaAction2: FC<SingleMetaAction2Props> = ({ className = '', post, comments_count = 0 }) => {
  return (
    <div className={`nc-SingleMetaAction2 ${className}`}>
      <div className="flex flex-row space-x-2.5 rtl:space-x-reverse items-center">
        <PostCardLikeAndComment
          itemClass="px-4 h-9 text-sm"
          hiddenCommentOnMobile
          useOnSinglePage
          className="!space-x-2.5 rtl:!space-x-reverse"
          comments_count={comments_count}
          id={post.id}
          likeCount={post.likes_count}
          isCancelGetCountLike={true}
          liked={post.is_liked}
        />
        <div className="px-1">
          <div className="border-s border-neutral-200 dark:border-neutral-700 h-6" />
        </div>
        <PostActionDropdown
          containerClassName="h-9 w-9 bg-neutral-100 hover:bg-neutral-200 dark:bg-neutral-800 dark:hover:bg-neutral-700"
          iconClass="h-5 w-5"
        />
      </div>
    </div>
  );
};

export default SingleMetaAction2;
