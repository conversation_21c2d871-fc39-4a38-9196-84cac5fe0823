'use client';

import React, { FC, useEffect, useRef, useState } from 'react';
import Tag from '@/components/Tag/Tag';
import SingleCommentForm from './SingleCommentForm';
import SingleCommentLists from './SingleCommentLists';
import useIntersectionObserver from '@/hooks/useIntersectionObserver';
import PostCardLikeAction from '@/components/PostCardLikeAction/PostCardLikeAction';
import PostCardCommentBtn from '@/components/PostCardCommentBtn/PostCardCommentBtn';
import { ArrowUpIcon } from '@heroicons/react/24/solid';
import { Post } from '@/contains/post';
import { useCommentStore } from '@/stores/useCommentStore';
import { useAdminNavigationStore } from '@/stores/useAdminNavigationStore';

export interface SingleContentProps {
  post: Post;
}

const SingleContent: FC<SingleContentProps> = ({ ...props }: SingleContentProps) => {
  const { post } = props;
  const endedAnchorRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const progressRef = useRef<HTMLButtonElement>(null);
  const shadowRef = useRef<HTMLDivElement>(null);

  const { totalComment } = useCommentStore();
  const { setActiveId } = useAdminNavigationStore();
  setActiveId('post', post.id);
  //
  const [isShowScrollToTop, setIsShowScrollToTop] = useState<boolean>(false);
  //

  const endedAnchorEntry = useIntersectionObserver(endedAnchorRef, {
    threshold: 0,
    root: null,
    rootMargin: '0%',
    freezeOnceVisible: false
  });

  useEffect(() => {
    const handleProgressIndicator = () => {
      const entryContent = contentRef.current;
      const progressBarContent = progressRef.current;

      if (!entryContent || !progressBarContent) {
        return;
      }

      const totalEntryH = entryContent.offsetTop + entryContent.offsetHeight;
      let winScroll =
        document.body.scrollTop || document.documentElement.scrollTop;
      let scrolled = ( winScroll / totalEntryH ) * 100;

      progressBarContent.innerText = scrolled.toFixed(0) + '%';

      if (scrolled >= 100) {
        setIsShowScrollToTop(true);
      } else {
        setIsShowScrollToTop(false);
      }
    };

    const handleProgressIndicatorHeadeEvent = () => {
      window?.requestAnimationFrame(handleProgressIndicator);
    };
    handleProgressIndicator();
    window?.addEventListener('scroll', handleProgressIndicatorHeadeEvent);
    return () => {
      window?.removeEventListener('scroll', handleProgressIndicatorHeadeEvent);
    };
  }, []);

  const showLikeAndCommentSticky =
    !endedAnchorEntry?.intersectionRatio &&
    ( endedAnchorEntry?.boundingClientRect.top || 0 ) > 0;

  useEffect(() => {
    if (!shadowRef.current) return;
    if (shadowRef.current.shadowRoot) return;

    const shadow = shadowRef.current.attachShadow({ mode: 'open' });

    const links = [
      `${process.env.ASSETS_URL}/themes/ripple/css/style.css?v=7.5.5`,
      `${process.env.ASSETS_URL}/vendor/core/core/base/libraries/ckeditor/content-styles.css`,
      `${process.env.ASSETS_URL}/themes/ripple/css/style.integration.css`,
    ];

    links.forEach((href) => {
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = href;
      shadow.appendChild(link);
    });

    const wrapperContainer = document.createElement('div');
    wrapperContainer.className = 'page-content';

    const wrapperArticle = document.createElement('article');
    wrapperArticle.className = 'post post--single';
    wrapperContainer.appendChild(wrapperArticle);

    const wrapperPostContent = document.createElement('div');
    wrapperPostContent.className = 'post__content';
    wrapperArticle.appendChild(wrapperPostContent);

    const wrapper = document.createElement('div');
    wrapper.className = 'comic__enable-theme ck-content';
    wrapper.innerHTML = post.content ?? '';
    wrapperPostContent.appendChild(wrapper);

    shadow.appendChild(wrapperContainer);

    const imgs = shadow.querySelectorAll('img');
    imgs.forEach((img) => {
      img.style.height = 'auto';
    });
  }, [post]);

  return (
    <div className="relative">
      <div className="nc-SingleContent space-y-10">
        {/* ENTRY CONTENT */}
        <div
          id="single-entry-content"
          ref={contentRef}
        >
          {/* post content */}
          <div
            ref={shadowRef}
            dangerouslySetInnerHTML={{ __html: post.content ?? '' }}
            className="comic__enable-theme"
          />
        </div>

        {/* TAGS */}
        <div className="max-w-screen-md mx-auto flex flex-wrap">
          {post.tags ? post.tags.map((item) => (
            <Tag hideCount key={item.id} tag={item} className="me-2 mb-2" />
          )) : <></>}
        </div>

        {/* COMMENT FORM */}
        <div
          id="comments"
          className="scroll-mt-20 max-w-screen-md mx-auto pt-5"
        >
          <h3 className="text-xl font-semibold text-neutral-800 dark:text-neutral-200">
            Phản hồi ({totalComment})
          </h3>
          <SingleCommentForm postId={post.id} isReply={false}/>
        </div>

        {/* COMMENTS LIST */}
        <div className="max-w-screen-md mx-auto">
          <SingleCommentLists postId={post.id} />
          <div ref={endedAnchorRef}></div>
        </div>
      </div>

      {/* action */}
      <div
        className={`sticky mt-8 bottom-8 z-40 justify-center ${
          showLikeAndCommentSticky ? 'flex' : 'hidden'
        }`}
      >
        <div className="bg-white dark:bg-neutral-800 shadow-lg rounded-full ring-1 ring-offset-1 ring-neutral-900/5 p-1.5 flex items-center justify-center space-x-2 rtl:space-x-reverse text-xs">
          <PostCardLikeAction
            className="px-3 h-9 text-xs"
            id={post.id}
            likeCount={post.likes_count}
            isCancelGetCountLike={true}
          />
          <div className="border-s h-4 border-neutral-200 dark:border-neutral-700"></div>
          <PostCardCommentBtn
            isATagOnSingle
            className={` flex px-3 h-9 text-xs`}
          />
          <div className="border-s h-4 border-neutral-200 dark:border-neutral-700"></div>

          <button
            className={`w-9 h-9 items-center justify-center bg-neutral-50 dark:bg-neutral-800 hover:bg-neutral-100 rounded-full ${
              isShowScrollToTop ? 'flex' : 'hidden'
            }`}
            onClick={() => {
              window.scrollTo({ top: 0, behavior: 'smooth' });
            }}
          >
            <ArrowUpIcon className="w-4 h-4" />
          </button>

          <button
            ref={progressRef}
            className={`w-9 h-9 items-center justify-center ${
              isShowScrollToTop ? 'hidden' : 'flex'
            }`}
            title="Go to top"
          >
            %
          </button>
        </div>
      </div>
    </div>
  );
};

export default SingleContent;
