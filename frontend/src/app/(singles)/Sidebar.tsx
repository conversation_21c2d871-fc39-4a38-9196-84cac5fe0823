import React, { FC } from 'react';
import WidgetAuthors from '@/components/WidgetAuthors/WidgetAuthors';
import WidgetCategories from '@/components/WidgetCategories/WidgetCategories';
import WidgetPosts from '@/components/WidgetPosts/WidgetPosts';
import WidgetTags from '@/components/WidgetTags/WidgetTags';
import { TCategory } from '@/contains/category';

export interface SidebarProps {
  categories?: TCategory[];
}

export const Sidebar: FC<SidebarProps> = ({ categories }: SidebarProps) => {
  return (
    <>
      <div className="mb-6">
        <WidgetTags />
      </div>
      <div className="mb-6">
        <WidgetCategories />
      </div>
      <div className="mb-6">
        <WidgetAuthors />
      </div>
      <WidgetPosts categories={categories} isHomePage={false} />
    </>
  );
};
