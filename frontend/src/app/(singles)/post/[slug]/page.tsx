import Image from 'next/image';
import SingleHeader from '@/app/(singles)/SingleHeader';
import { IPost, IPostDetail, Post } from '@/contains/post';
import SingleContent from '@/app/(singles)/SingleContent';
import { Sidebar } from '@/app/(singles)/Sidebar';
import { Metadata } from 'next';
import SingleRelatedPosts from '@/app/(singles)/SingleRelatedPosts';
import { ENDPOINT } from '@/contains/endpoint';
import { notFound } from 'next/navigation';
import CountViewClient from '@/components/CountViewClient';
import { postServerApi } from '@/apis/postServerApi';
import { handleConfigMetaSeo } from '@/lib/configMetaSeo';
import { metaSeoApi } from '@/apis/metaSeoApi';
import { IMetaSeo } from '@/contains/types';
import { getThemeOption } from '@/lib/theme.server';

interface PageSingleTemplate3 {
  params: {
    slug: string
  },
}

export async function generateMetadata(
  { params }: PageSingleTemplate3
): Promise<Metadata> {
  const { slug } = params;

  const [themeData, postDetail, postMetaSeo] = await Promise.all([
    getThemeOption(),
    getPostDetail(slug),
    metaSeoApi.get<IMetaSeo>({
      params: {
        slug,
        model: 'post',
        lang: 'en'
      }
    })
  ]);

  const { site_title_separator = '-', site_title = '' } = themeData?.general || {};
  const separator = site_title_separator || '-';
  const siteTitle = site_title || 'Cuuduatin';

  const postMetaSeoDetail = postMetaSeo?.data;
  const titlePage = `${postMetaSeoDetail?.meta_value_custom?.seo_title || postDetail?.posts.name} ${separator} ${siteTitle}`;
  const descriptionPage = `${postMetaSeoDetail?.meta_value_custom?.seo_description || postDetail?.posts.description || 'Nội dung bài viết'}`;
  const { robots, canonical } = handleConfigMetaSeo({
    slug: slug ?? '',
    indexFollow: postMetaSeoDetail?.meta_value_custom?.index ?? '',
    type: ''
  });

  const imageUrl = postDetail?.posts.image || themeData?.general.seo_og_image || '';

  return {
    title: titlePage,
    description: descriptionPage,
    alternates: {
      canonical
    },
    other: robots,
    openGraph: {
      title: titlePage,
      description: descriptionPage,
      url: canonical,
      siteName: siteTitle,
      images: [
        {
          url: imageUrl,
          width: 1200,
          height: 630,
          alt: postDetail?.posts.name || '',
        }
      ],
      type: 'article',
    },
    twitter: {
      card: 'summary_large_image',
      title: titlePage,
      description: descriptionPage,
      images: [imageUrl],
    }
  };
}

const getPostByAuthor = async (authorId: string) => {
  const res: IPost = await postServerApi.get<IPost>({
    endpoint: ENDPOINT.GET_POSTS_FILTERS_SCORE,
    params: {
      'author[]': authorId,
      page: 1,
      per_page: 8
    }
  }) as IPost;
  return res?.data ?? [];
};

const getPostDetail = async (slug: string) => {
  const res: IPostDetail = await postServerApi.get<IPostDetail>({
    endpoint: slug
  }) as IPostDetail;

  if (!res?.data) {
    notFound();
  }

  let restAuthor: Post[] = [];

  if (res.data.author?.id) {
    const authorDetail = await getPostByAuthor(res.data.author.id.toString());
    if (authorDetail) {
      restAuthor = authorDetail;
    }
  }

  return {
    posts: res.data,
    postByAuthor: restAuthor ?? []
  };
};

export default async function Page({ params }: PageSingleTemplate3) {
  const slug = params.slug ?? '';
  const detail = await getPostDetail(slug);

  return (
    <>
      <header className="relative pt-16 pb-4 z-10 md:py-20 lg:py-28 bg-neutral-900 dark:bg-black">
        {/* SINGLE HEADER */}
        <div className="dark container relative z-10">
          <div className="max-w-screen-md">
            <SingleHeader post={detail.posts} />
          </div>
        </div>

        {/* FEATURED IMAGE */}
        <div className="mt-8 md:mt-0 md:absolute md:top-0 md:end-0 md:bottom-0 md:w-1/2 lg:w-2/5 2xl:w-1/3">
          <div className="hidden md:block absolute top-0 start-0 bottom-0 w-1/5 from-neutral-900 dark:from-black bg-gradient-to-r rtl:bg-gradient-to-l"></div>
          {detail?.posts?.image ? <Image
            className="block w-full h-full object-cover"
            src={detail?.posts?.image}
            alt=""
            width={1635}
            height={774}
            sizes="(max-width: 1024px) 100vw, 1240px"
          /> : <></>}
        </div>
      </header>

      <div className="container flex flex-col my-10 lg:flex-row ">
        <div className="w-full lg:w-3/5 xl:w-2/3 xl:pe-20">
          <SingleContent post={detail.posts} />
        </div>
        <div className="w-full mt-12 lg:mt-0 lg:w-2/5 lg:ps-10 xl:ps-0 xl:w-1/3">
          <Sidebar categories={detail.posts.categories} />
        </div>
      </div>
      <SingleRelatedPosts moreFromAuthorPosts={detail.postByAuthor} />
      <CountViewClient postId={detail.posts.id} />
    </>
  );
};
