'use client';

import React, { FC } from 'react';
import ButtonPrimary from '@/components/Button/ButtonPrimary';
import Textarea from '@/components/Textarea/Textarea';
import Button from '@/components/Button/Button';
import { SubmitHandler, useForm } from 'react-hook-form';
import { schemaSubmitComment, SubmitCommentFormValues } from '@/data/validation';
import { yupResolver } from '@hookform/resolvers/yup';
import { toast } from 'react-toastify';
import { useCommentStore } from '@/stores/useCommentStore';
import { TCommentItem } from '@/contains/post';
import { useRouter } from 'next/navigation';
import { useUserServer } from '@/hooks/useUser';

export interface SingleCommentFormProps {
  className?: string;
  onClickSubmit?: () => void;
  onClickCancel?: () => void;
  textareaRef?: React.MutableRefObject<null>;
  defaultValue?: string;
  rows?: number;
  comment?: TCommentItem;
  postId?: number;
  isReply: boolean;
}

const SingleCommentForm: FC<SingleCommentFormProps> = ({
  className = 'mt-5',
  onClickSubmit,
  onClickCancel,
  textareaRef,
  defaultValue = '',
  rows = 4,
  comment,
  postId,
  isReply
}) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<SubmitCommentFormValues>({
    resolver: yupResolver(schemaSubmitComment),
    defaultValues: {
      content: ''
    }
  });
  const { replyComment, editComment, createComment, isEdit } = useCommentStore();
  const { data: user } = useUserServer();
  const router = useRouter();
  const onSubmit: SubmitHandler<SubmitCommentFormValues> = async (data) => {
    try {
      if (isEdit) {
        await editComment({
          id: comment?.id.toString() ?? '',
          content: data.content
        }).finally(() => {
          onClickSubmit && onClickSubmit();
        });
      } else {
        if (isReply){
          await replyComment({
            id: postId?.toString() ?? '',
            idComment: comment?.id.toString() ?? '',
            content: data.content
          }).finally(() => {
            onClickSubmit && onClickSubmit();
          });
        }else{
          await createComment({
            id: postId?.toString() ?? '',
            content: data.content
          }).finally(() => {
            onClickSubmit && onClickSubmit();
          });
        }
      }
    } catch {
      toast.error('Bình luận không thành công');
    } finally {
      reset();
    }
  };
  return (
    <form onSubmit={handleSubmit(onSubmit)} className={`nc-SingleCommentForm ${className}`}>
      <label>
        <Textarea
          placeholder="Bình luận..."
          required={true}
          defaultValue={defaultValue}
          rows={rows}
          {...register('content')}
          disabled={!user?.data?.id}
        />
        {errors.content && <p className="text-red-500 text-sm mt-1">{errors.content.message}</p>}
      </label>
      <div className="mt-2 space-x-3">
        {!user?.data?.id ?
          <ButtonPrimary type="button" href={'/login'} onClick={()=>{
            router.replace('/login')
            window.scrollTo(0, 0);
          }}>
            Đăng nhập để bình luận
          </ButtonPrimary> :
          <>
            <ButtonPrimary type="submit">
              Gửi
            </ButtonPrimary>
            <Button
              type="button" pattern="white" onClick={() => {
              onClickCancel && onClickCancel();
              reset();
            }}
            >
              Hủy
            </Button>
          </>}
      </div>
    </form>
  );
};

export default SingleCommentForm;
