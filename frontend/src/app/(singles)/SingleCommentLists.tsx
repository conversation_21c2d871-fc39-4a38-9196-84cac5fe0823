'use client';
import React, { FC, useEffect, useState } from 'react';
import CommentCard from '@/components/CommentCard/CommentCard';
import { TAuthorComment, TCommentItem } from '@/contains/post';
import { useCommentStore } from '@/stores/useCommentStore';
import { interactionApi } from '@/apis/interactionApi';
import PaginationClient from '@/components/Pagination/PaginationClient';
import CommentCardSkeleton from '@/components/CommentCard/CommentCardSkeleton';
import { useLocation } from 'react-use';

export interface ISingleCommentListsProps {
  postId: number;
}

const PER_PAGE = 10;

const SingleCommentLists: FC<ISingleCommentListsProps> = ({ ...props }: ISingleCommentListsProps) => {
  const { postId } = props;
  const [listCommentData, setListCommentData] = useState<TCommentItem[]>([]);
  const [page, setPage] = useState<number>(1);
  const [loading, setLoading] = useState<boolean>(false);
  const location = useLocation();
  const {
    content,
    isDelete,
    isEdit,
    totalComment,
    setIsDelete,
    setContent,
    setIsEdit,
    setTotalComment
  } = useCommentStore();

  const handleGetComments = async () => {
    setLoading(true);
    try {
      const res = await interactionApi.get({
        endpoint: `comments/filters`,
        params: {
          reference_id: postId,
          page: page,
          per_page: PER_PAGE
        }
      });
      const data = res.data ?? [];
      const meta = res.meta;
      setListCommentData(data);
      setTotalComment(meta.total ?? 0);
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
      const hash = location.hash;
      if (hash) {
        const anchorId = hash.slice(1);
        const el = document.getElementById(anchorId);
        if (el) {
          el.scrollIntoView({ behavior: 'smooth', block: 'start', inline: 'start' });
        }
      }
    }
  };

  useEffect(() => {
    if (!postId) {
      return;
    }
    handleGetComments().finally();
  }, [page]);

  useEffect(() => {
    if (!content.id) return;

    if (isDelete) {
      setListCommentData((prev) =>
        prev.filter((comment) => comment.id !== Number(content.id))
      );
      setIsDelete(false);
    } else if (isEdit) {
      setListCommentData((prev) =>
        prev.map((comment) =>
          comment.id === Number(content.id)
            ? { ...comment, id: Number(content.id), content: content.content }
            : comment
        )
      );
      setIsEdit(false);
    } else {
      page === 1 ? handleGetComments().finally() : setPage(1);
    }

    setContent({ id: '', content: '' });
  }, [content]);

  return (
    <ul className="nc-SingleCommentLists space-y-5">
      {loading ? <>
        {Array(PER_PAGE).fill(0).map((_, index) => (
          <CommentCardSkeleton
            key={index}
          /> )
        )}
      </> : <>
        {listCommentData.map((comment) => <CommentCard
          key={comment.id}
          comment={comment}
          postId={postId}
        />)}
      </>}


      {/* PAGINATIONS */}
      <div className="flex flex-col mt-12 lg:mt-16 space-y-5 sm:space-y-0 sm:space-x-3 sm:flex-row sm:justify-between sm:items-center">
        <PaginationClient perPage={PER_PAGE} total={totalComment} currentPage={page} onPageChange={setPage} />
      </div>
    </ul>
  );
};

export default SingleCommentLists;
