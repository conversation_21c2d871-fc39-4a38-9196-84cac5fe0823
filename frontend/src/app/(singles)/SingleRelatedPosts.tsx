import React, { FC } from 'react';
import Heading from '@/components/Heading/Heading';
import Card11 from '@/components/Card11/Card11';
import Card9 from '@/components/Card9/Card9';
import { IPost, Post } from '@/contains/post';
import { postServerApi } from '@/apis/postServerApi';
import { ENDPOINT } from '@/contains/endpoint';

export interface SingleRelatedPostsProps {
  moreFromAuthorPosts?: Post[];
}


const getPostData = async () => {
  const res: IPost = await postServerApi.get<IPost>({
    endpoint: ENDPOINT.GET_POSTS_FILTERS_SCORE,
    params: {
      order_by: 'updated_at',
      order: 'desc',
      page: 1,
      per_page: 4
    }
  }) as IPost;
  return res?.data ?? [];
};

const SingleRelatedPosts: FC<SingleRelatedPostsProps> = async ({
  moreFromAuthorPosts = []
}) => {
  const relatedPosts = await getPostData();
  return (
    <div className="relative bg-neutral-100 dark:bg-neutral-800 py-16 lg:py-28 mt-16 lg:mt-28">
      {/* RELATED  */}
      <div className="container">
        <div>
          <Heading
            className="mb-10 text-neutral-900 dark:text-neutral-50"
            desc=""
          >
            Bài viết liên quan
          </Heading>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 md:gap-8">
            {relatedPosts.map((post) => (
              <Card11 key={post.id} post={post} />
            ))}
          </div>
        </div>

        {/* MORE FROM AUTHOR */}
        <div className="mt-20">
          <Heading
            className="mb-10 text-neutral-900 dark:text-neutral-50"
            desc=""
          >
            Bài viết có cùng tác giả
          </Heading>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 md:gap-8">
            {moreFromAuthorPosts.map((post) => (
              <Card9 key={post.id} post={post} />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SingleRelatedPosts;
