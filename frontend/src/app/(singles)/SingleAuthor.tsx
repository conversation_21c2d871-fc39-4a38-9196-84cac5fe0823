import Avatar from '@/components/Avatar/Avatar';
import React, { FC } from 'react';
import { TAuthor } from '@/contains/author';
import { LinkRoute } from '@/components/LinkRoute';

export interface SingleAuthorProps {
  author: TAuthor;
}

const SingleAuthor: FC<SingleAuthorProps> = ({ author }: SingleAuthorProps) => {
  const fullName: string = `${author?.first_name ?? ''} ${author?.last_name ?? ''}`.trim() || 'Author';

  return (
    <div className="nc-SingleAuthor flex">
      <LinkRoute
        href={author.username ? `/author/${author.username}` : '/'}
      >
        <Avatar
          imgUrl={author?.image ?? ''}
          userName={fullName}
          sizeClass="h-12 w-12 text-lg sm:text-xl sm:h-24 sm:w-24"
        />
      </LinkRoute>
      <div className="flex flex-col ml-3 max-w-lg sm:ml-5">
        <span className="text-xs text-neutral-400 uppercase tracking-wider">
          WRITTEN BY
        </span>
        <h2 className="text-lg font-semibold text-neutral-900 dark:text-neutral-200">
          <div
            // href={author.href}
          >{fullName}</div>
        </h2>
        <span className="block mt-1 text-sm text-neutral-500 sm:text-base dark:text-neutral-300">
          {/*{author.desc}*/}
          <div
            className="text-primary-6000 font-medium ml-1"
            // href={author.href}
          >
            Read more
          </div>
        </span>
      </div>
    </div>
  );
};

export default SingleAuthor;
