import { PropsWithChildren } from 'react';
import { Metadata } from 'next';
import { IAuthorDetail } from '@/contains/author';
import { authorApi } from '@/apis/authorApi';
import { getThemeOption } from '@/lib/theme.server';
import { notFound } from 'next/navigation';
import { handleConfigMetaSeo, handleConfigOpenGraph } from '@/lib/configMetaSeo';

interface ILayoutAuthorWrapper {
  params: {
    slug: string;
  };
}

export async function generateMetadata(
  { params }: ILayoutAuthorWrapper
): Promise<Metadata> {

  const { slug } = params;

  const [resAuthorDetail, themeData] = await Promise.all([
    authorApi.get<IAuthorDetail>({ endpoint: slug ?? '' }),
    getThemeOption()
  ]);
  if (!resAuthorDetail.data) {
    return notFound();
  }

  const authorDetail = resAuthorDetail as IAuthorDetail;

  const {
    site_title_separator = '-',
    site_title = '',
    seo_description,
    site_description,
    seo_title,
    seo_index
  } = themeData?.general || {};

  const separator = site_title_separator || '-';
  const siteTitle = site_title || 'Cuuduatin';
  const fullName: string = `${authorDetail?.data?.first_name ?? ''} ${authorDetail?.data?.last_name ?? ''}`.trim() || '';
  const titlePage = `Tác giả ${fullName} ${separator} ${siteTitle}`;
  const descriptionPage = seo_description || site_description || seo_title || '';
  const { robots, canonical } = handleConfigMetaSeo({
    indexFollow: !!seo_index ? 'index' : '',
    type: 'author',
    slug
  });

  const imageUrl = themeData?.general.seo_og_image || '';

  const { twitter, openGraph } = handleConfigOpenGraph({
    themeData,
    titlePage,
    descriptionPage,
    canonical,
    siteTitle,
    altImage: `Tác giả ${fullName}`|| '',
    imageUrl
  });

  return {
    title: titlePage,
    description: descriptionPage,
    alternates: {
      canonical
    },
    other: robots,
    openGraph,
    twitter
  };
}

const LayoutAuthor = ({ children }: PropsWithChildren) => {
  return children;
};
export default LayoutAuthor;
