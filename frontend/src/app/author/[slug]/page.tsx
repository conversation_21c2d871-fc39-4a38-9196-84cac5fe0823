import React from 'react';
import { IAuthorDetail, TAuthor } from '@/contains/author';
import { authorApi } from '@/apis/authorApi';
import { ITEM_PER_PAGE_16 } from '@/utils/constant';
import NcImage from '@/components/NcImage/NcImage';
import Image from 'next/image';
// import SectionSubscribe2 from '@/components/SectionSubscribe2/SectionSubscribe2';
import { IMG_PLACEHOLDER } from '@/contains/contants';
import ListPostAuthor from '@/components/PostListServer';
import { AuthorHeader } from '@/components/AuthorHeader';
import { ActionAuthor } from '@/components/AuthorHeader/ActionAuthor';
import { notFound } from 'next/navigation';

interface IPageAuthor {
  params: {
    slug: string;
  };
  searchParams?: {
    [key: string]: string | undefined;
  };
}

const getAuthorDetail = async (queryParams: { slug: string, page: number, per_page: number }): Promise<TAuthor | null> => {
  const { slug, page, per_page } = queryParams;
  try {
    const authorResponse = await authorApi.get<IAuthorDetail>({
      endpoint: slug,
      params: { page, per_page }
    }) as IAuthorDetail;
    if (!authorResponse?.data) return null;
    return authorResponse.data as TAuthor;
  } catch (error) {
    console.error('Error fetching author detail:', error);
    return null;
  }
};

const PageAuthor = async ({ params, searchParams }: IPageAuthor) => {
  const slug = params.slug ?? '';
  const page = parseInt(searchParams?.page || '1');
  const per_page = parseInt(searchParams?.per_page || `${ITEM_PER_PAGE_16}`);

  const detail = await getAuthorDetail({ slug, page, per_page });

  if (!detail) {
    return notFound();

  }

  const { posts, image } = detail;

  return (
    <div className={`nc-PageAuthor`}>
      {/* HEADER */}
      <div className="w-full">
        <div className="relative w-full h-40 md:h-60 2xl:h-72">
          <NcImage
            alt=""
            containerClassName="absolute inset-0"
            sizes="(max-width: 1280px) 100vw, 1536px"
            src="https://images.pexels.com/photos/459225/pexels-photo-459225.jpeg?auto=compress&cs=tinysrgb&dpr=2&h=750&w=1260"
            className="object-cover w-full h-full"
            fill
            priority
          />
        </div>

        <div className="container -mt-10 lg:-mt-16">
          <div className="relative bg-white dark:bg-neutral-900 dark:border dark:border-neutral-700 p-5 lg:p-8 rounded-3xl md:rounded-[40px] shadow-xl flex flex-col md:flex-row">
            <div className="w-32 lg:w-40 flex-shrink-0 mt-12 sm:mt-0">
              <div className="wil-avatar relative flex-shrink-0 inline-flex items-center justify-center overflow-hidden text-neutral-100 uppercase font-semibold rounded-full w-20 h-20 text-xl lg:text-2xl lg:w-36 lg:h-36 ring-4 ring-white dark:ring-0 shadow-2xl z-0">
                <Image
                  alt="Avatar"
                  src={image ?? IMG_PLACEHOLDER}
                  fill
                  className="object-cover"
                  priority
                />
              </div>
            </div>
            <AuthorHeader author={detail} />
            <ActionAuthor />
          </div>
        </div>
      </div>

      {/* BODY */}
      <div className="container py-16 lg:pb-28 lg:pt-20 space-y-16 lg:space-y-28">
        <ListPostAuthor posts={posts} />
        {/*<SectionSubscribe2 />*/}
      </div>
    </div>
  );
};

export default PageAuthor;
