'use client';

import React from 'react';
import HeaderLogged from '@/components/Header/HeaderLogged';
import { useThemeMode } from '@/hooks/useThemeMode';
import { IMenuResponse, IThemeOptions } from '@/contains/types';

interface ISiteHeader {
  themeData: IThemeOptions | null;
  menuData: IMenuResponse | null;
}

const SiteHeader: React.FC<ISiteHeader> = ({ ...props }: ISiteHeader) => {
  const { themeData, menuData } = props;
  useThemeMode();

  return <HeaderLogged themeData={themeData} menuData={menuData} />;
};

export default SiteHeader;
