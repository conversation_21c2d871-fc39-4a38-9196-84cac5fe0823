import './globals.css';
import '@/styles/index.scss';
import { Poppins } from 'next/font/google';
import Footer from '@/components/Footer/Footer';
import SiteHeader from './SiteHeader';
import { ToastContainer } from 'react-toastify';
import React from 'react';
import { Metadata, Viewport } from 'next';
import { SWRConfig } from 'swr';
import { KEY_SWR } from '@/contains/keySWR';
import { getFooter, getMenu, getThemeOption } from '@/lib/theme.server';
import { IMenuResponse, IThemeOptions } from '@/contains/types';
import { generateMetaFromTheme } from '@/utils/generateMetaFromTheme';
import { handleConfigMetaSeo } from '@/lib/configMetaSeo';
import { generateColorCSSVariables } from '@/utils/generateColorCSS';
import { config, library } from '@fortawesome/fontawesome-svg-core';
import '@fortawesome/fontawesome-svg-core/styles.css';
import { fab } from '@fortawesome/free-brands-svg-icons';
import { cookies } from 'next/headers';
import { getUserData } from '@/lib/user.server';

config.autoAddCss = false;
library.add(fab);

export async function generateMetadata(): Promise<Metadata> {
  const theme = await getThemeOption();
  const meta = generateMetaFromTheme(theme);

  if (!meta) {
    return {};
  }

  const seoIndex = theme?.general?.seo_index;
  const { robots, canonical } = handleConfigMetaSeo({
    indexFollow: !!seoIndex ? 'index' : '',
    type: ''
  });

  return {
    title: meta.title,
    description: meta.description,
    openGraph: {
      title: meta.title,
      description: meta.description,
      url: 'https://cslant.com',
      siteName: meta.title,
      type: 'website',
      images: [
        {
          url: theme?.general.seo_og_image??'',
          width: 1200,
          height: 630,
          alt: meta.title,
        }
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: meta.title,
      description: meta.description,
      images: [theme?.general.seo_og_image??'',],
    },
    icons: {
      icon: [
        { url: meta.favicon, type: 'image/x-icon' }
      ]
    },
    alternates: {
      canonical
    },
    other: robots
  };
}

const poppins = Poppins({
  subsets: ['latin'],
  display: 'swap',
  weight: ['300', '400', '500', '600', '700']
});

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false
};

function generateTypographyCSS(theme: IThemeOptions | null) {
  const typography = theme?.typography;
  if (!typography) {
    return '';
  }

  const safeNumber = (val: any, fallback: number) =>
    typeof val === 'number' ? val : fallback;

  const safeString = (val: any, fallback: string) =>
    typeof val === 'string' ? val : fallback;

  const fontFamily = `${safeString(typography.tp_primary_font, 'inherit')}, ${poppins.style.fontFamily}`;

  return `
    * {
      font-family: ${fontFamily} !important;
    }

    .comic__enable-theme {
      font-size: ${safeNumber(typography.tp_body_size, 16)}px !important;
    }
    
    .comic__enable-theme * {
      font-size: inherit !important;
    }

    .comic__enable-theme h1 {
      font-size: ${safeNumber(typography.tp_h1_size, 28)}px !important;
    }

    .comic__enable-theme h2 {
      font-size: ${safeNumber(typography.tp_h2_size, 24)}px !important;
    }

    .comic__enable-theme h3 {
      font-size: ${safeNumber(typography.tp_h3_size, 22)}px !important;
    }

    .comic__enable-theme h4 {
      font-size: ${safeNumber(typography.tp_h4_size, 20)}px !important;
    }

    .comic__enable-theme h5 {
      font-size: ${safeNumber(typography.tp_h5_size, 18)}px !important;
    }

    .comic__enable-theme h6 {
      font-size: ${safeNumber(typography.tp_h6_size, 16)}px !important;
    }
  `;
}

export default async function RootLayout({
  children
}: {
  children: React.ReactNode;
}) {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;

  const [themeData, menuData, footerData, userData] = await Promise.all([
    getThemeOption(),
    getMenu(),
    getFooter(),
    token ? getUserData() : Promise.resolve({
      data: null,
      error: null,
      meta: false
    }),
  ]);

  const dynamicStyle = generateTypographyCSS(themeData);
  const cssVars = generateColorCSSVariables(themeData?.general.primary_color || '#099419');
  const combinedCSS = `${dynamicStyle}\n${cssVars}`;

  return (
    <html lang="en" className={poppins.className}>
    <head>
      <style
        // remove the warning about hydration warning
        suppressHydrationWarning
        dangerouslySetInnerHTML={{ __html: combinedCSS }}
      />
    </head>
    <body>
    <div className="bg-[#f8f8f8] text-base dark:bg-neutral-900/95 text-neutral-900 dark:text-neutral-200 min-h-dvh">
      <SWRConfig
        value={{
          fallback: {
            [KEY_SWR.GET_THEME_OPTIONS]: themeData,
            [KEY_SWR.GET_AUTH]: userData,
            [KEY_SWR.GET_MENUS]: menuData.data,
            [KEY_SWR.GET_FOOTERS]: footerData.data
          }
        }}
      >
        <SiteHeader themeData={themeData} menuData={menuData as unknown as IMenuResponse} />
        {children}
        <Footer themeData={themeData} menuItem={menuData.data ?? []} footerItem={footerData.data ?? []} />
      </SWRConfig>
    </div>
    <ToastContainer />
    </body>
    </html>
  );
}
