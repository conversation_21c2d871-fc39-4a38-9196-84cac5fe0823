import { Route } from '@/routers/types';
import _ from 'lodash';
import { INavItemType } from '@/contains/types';

const randomId = _.uniqueId;

export const MEGAMENU_TEMPLATES: INavItemType[] = [
  // home pages ---------
  {
    id: randomId(),
    href: "/#",
    name: "Home Page",
    children: [
      { id: randomId(), href: "/", name: "Home page" },
    ],
  },

  // single pages ---------
  {
    id: randomId(),
    href: "/single/demo-slug" as Route,
    name: "Single Pages",
    children: [
      {
        id: randomId(),
        href: "/single/demo-slug" as Route,
        name: "Single page",
      },
    ],
  },

  // archive pages ---------
  {
    id: randomId(),
    href: "/#",
    name: "Archive Pages",
    children: [

      {
        id: randomId(),
        href: "/search",
        name: "Search page",
      },
      {
        id: randomId(),
        href: "/author/demo-slug" as Route,
        name: "Author page",
      },
    ],
  },

  // others pages ----------------
  {
    id: randomId(),
    href: "/#",
    name: "Other Pages",
    children: [
      { id: randomId(), href: "/about", name: "About" },
      { id: randomId(), href: "/contact", name: "Contact us" },
      {
        id: randomId(),
        href: "/login",
        name: "Login",
      },
      {
        id: randomId(),
        href: "/signup",
        name: "Signup",
      },
      {
        id: randomId(),
        href: "/forgot-pass",
        name: "Forgot password",
      },
    ],
  },
];


export const NAVIGATION_DEMO_2: INavItemType[] = [
  // single pages ----------------
  {
    id: randomId(),
    href: "/single/demo-slug" as Route,
    name: "Singles",
    type: "dropdown",
    children: [
      {
        id: randomId(),
        href: "/single-audio/demo-slug" as Route,
        name: "Single Audio",
      },
      {
        id: randomId(),
        href: "/single-video/demo-slug" as Route,
        name: "Single Video",
      },
      {
        id: randomId(),
        href: "/single-gallery/demo-slug" as Route,
        name: "Single Gallery",
        isNew: true,
      },
    ],
  },

  {
    id: randomId(),
    href: "/search",
    name: "Templates",
    type: "megaMenu",
    children: MEGAMENU_TEMPLATES,
  },
];


export const NAVIGATION_HOME: INavItemType[] = [
  {
    id: randomId(),
    href: "/tag" as Route,
    name: "Thẻ",
  },
  {
    id: randomId(),
    href: "/category" as Route,
    name: "Danh mục",
  },
];

export const NAVIGATION_ADMIN: INavItemType[] = [
  {
    id: randomId(),
    href: "/admin",
    name: "Trang admin",
  },
  {
    id: randomId(),
    href: "" as Route,
    name: "Menu quản lý",
    type: "dropdown",
    children: [
      {
        id: randomId(),
        href: "/admin/blog/posts" as Route,
        name: "Quản lý bài viết",
      },
      {
        id: randomId(),
        href: "/admin/blog/categories" as Route,
        name: "Chỉnh sửa danh mục",
      },
      {
        id: randomId(),
        href: "/admin/blog/tags" as Route,
        name: "Chỉnh sửa thẻ",
      },
    ],
  },
];

export const NAVIGATION_EDITOR: INavItemType[] = [
  {
    id: randomId(),
    href: "/admin",
    name: "Trang admin",
  },
  {
    id: randomId(),
    href: "/admin/blog/posts",
    name: "Chỉnh sửa bài viết",
  },
];

