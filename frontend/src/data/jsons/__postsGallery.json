[{"index": 10010101, "id": "9e3e3994-a3ed-47ca-as014-d4483884cfe2", "featuredImage": "https://images.unsplash.com/photo-1440778303588-435521a205bc?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80", "title": "Lenovo’s smarter devices stoke professional passions ", "desc": "Aenean lectus. Pellentesque eget nunc. Donec quis orci eget orci vehicula condimentum.", "date": "May 20, 2021", "href": "/single-gallery/this-is-single-slug", "commentCount": 11, "viewdCount": 2504, "readingTime": 2, "bookmark": {"count": 3007, "isBookmarked": false}, "like": {"count": 3366, "isLiked": true}, "authorId": 3, "categoriesId": [3, 12], "postType": "gallery", "galleryImgs": ["https://images.pexels.com/photos/2363814/pexels-photo-2363814.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/4352244/pexels-photo-4352244.jpeg?auto=compress&cs=tinysrgb&dpr=2&h=750&w=1260", "https://images.pexels.com/photos/3023211/pexels-photo-3023211.jpeg?auto=compress&cs=tinysrgb&dpr=2&h=750&w=1260", "https://images.pexels.com/photos/4982737/pexels-photo-4982737.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/4066850/pexels-photo-4066850.jpeg?auto=compress&cs=tinysrgb&dpr=2&h=750&w=1260"]}, {"index": 21001, "id": "af92a665-4a4d-4cff-9e17-89456dfs21fb5", "featuredImage": "https://images.unsplash.com/photo-1489753735160-2cbf3d9006d4?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1050&q=80", "title": "How AI and Teams are benefitting the littlest of patients ", "desc": "In sagittis dui vel nisl. Duis ac nibh. Fusce lacus purus, aliquet at, feugiat non, pretium quis, lectus.", "date": "May 20, 2021", "href": "/single-gallery/this-is-single-slug", "commentCount": 13, "viewdCount": 1646, "readingTime": 5, "bookmark": {"count": 3751, "isBookmarked": false}, "like": {"count": 3024, "isLiked": false}, "authorId": 4, "categoriesId": [4, 13], "postType": "gallery", "galleryImgs": ["https://images.pexels.com/photos/4526153/pexels-photo-4526153.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/3097112/pexels-photo-3097112.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/4273440/pexels-photo-4273440.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/892617/pexels-photo-892617.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/1029138/pexels-photo-1029138.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500"]}, {"index": 31002, "id": "dffe0224-ebff-4803-bb66-e8dd128656284", "featuredImage": "https://images.unsplash.com/photo-1418854982207-12f710b74003?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80", "title": "DIYer and TV host <PERSON><PERSON>’s journey through gaming keeps evolving", "desc": "Integer tincidunt ante vel ipsum. Praesent blandit lacinia erat. Vestibulum sed magna at nunc commodo placerat.", "date": "May 20, 2021", "href": "/single-gallery/this-is-single-slug", "commentCount": 33, "viewdCount": 4031, "readingTime": 2, "bookmark": {"count": 76, "isBookmarked": false}, "like": {"count": 222, "isLiked": false}, "authorId": 4, "categoriesId": [4, 13], "postType": "gallery", "galleryImgs": ["https://images.pexels.com/photos/1457812/pexels-photo-1457812.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/2507007/pexels-photo-2507007.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/4254555/pexels-photo-4254555.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/4983184/pexels-photo-4983184.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/2549018/pexels-photo-2549018.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500"]}, {"index": 41001, "id": "5ac7cb90-4694-42e8-8883-16372f711eaa8", "featuredImage": "https://images.unsplash.com/photo-1581610489881-f316ffcf0424?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80", "title": "New tools for Black pregnant and postpartum mothers to save lives", "desc": "Fusce consequat. Nulla nisl. Nunc nisl.", "date": "May 20, 2021", "href": "/single-gallery/this-is-single-slug", "commentCount": 49, "viewdCount": 1632, "readingTime": 6, "bookmark": {"count": 264, "isBookmarked": true}, "like": {"count": 3735, "isLiked": true}, "authorId": 5, "categoriesId": [5, 14], "postType": "gallery", "galleryImgs": ["https://images.pexels.com/photos/2394446/pexels-photo-2394446.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/7026306/pexels-photo-7026306.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/4871012/pexels-photo-4871012.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/3016353/pexels-photo-3016353.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/4254553/pexels-photo-4254553.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500"]}, {"index": 51002, "id": "f82a8455-3a14-4af9-b6d2-ac6cd74e0d07c", "featuredImage": "https://images.unsplash.com/photo-1448518340475-e3c680e9b4be?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1951&q=80", "title": "People who inspired us in 2019 ", "desc": "Aliquam quis turpis eget elit sodales scelerisque. <PERSON><PERSON><PERSON> sit amet eros. Suspendisse accumsan tortor quis turpis.", "date": "May 20, 2021", "href": "/single-gallery/this-is-single-slug", "commentCount": 9, "viewdCount": 3338, "readingTime": 5, "bookmark": {"count": 733, "isBookmarked": false}, "like": {"count": 3569, "isLiked": true}, "authorId": 10, "categoriesId": [10, 19], "postType": "gallery", "galleryImgs": ["https://images.pexels.com/photos/3651577/pexels-photo-3651577.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/8065131/pexels-photo-8065131.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/5282243/pexels-photo-5282243.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/4340670/pexels-photo-4340670.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/5043948/pexels-photo-5043948.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500"]}, {"index": 61002, "id": "54c11c92-049f-4353-8cdb-f7fsec70d3ae75", "featuredImage": "https://images.pexels.com/photos/4397925/pexels-photo-4397925.jpeg?auto=compress&cs=tinysrgb&dpr=2&h=750&w=1260", "title": "How architects visualize design for world’s biggest airport", "desc": "Phasellus sit amet erat. Nulla tempus. Vivamus in felis eu sapien cursus vestibulum.", "date": "May 20, 2021", "href": "/single-gallery/this-is-single-slug", "commentCount": 13, "viewdCount": 4475, "readingTime": 2, "bookmark": {"count": 199, "isBookmarked": false}, "like": {"count": 3052, "isLiked": true}, "authorId": 9, "categoriesId": [9, 18], "postType": "gallery", "galleryImgs": ["https://images.pexels.com/photos/4492596/pexels-photo-4492596.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/7891883/pexels-photo-7891883.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/2914152/pexels-photo-2914152.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/346885/pexels-photo-346885.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/1008155/pexels-photo-1008155.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500"]}, {"index": 71003, "id": "1c40aaaf-0077-4f54-9b34-fse90d95e17c7e", "featuredImage": "https://images.unsplash.com/photo-1518609878373-06d740f60d8b?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80", "title": "Take a 3D tour through a Microsoft datacenter", "desc": "Vestibulum ac est lacinia nisi venenatis tristique. <PERSON><PERSON><PERSON> congue, diam id ornare imperdiet, sapien urna pretium nisl, ut volutpat sapien arcu sed augue. Aliquam erat volutpat.", "date": "May 20, 2021", "href": "/single-gallery/this-is-single-slug", "commentCount": 20, "viewdCount": 2714, "readingTime": 4, "bookmark": {"count": 1321, "isBookmarked": true}, "like": {"count": 3309, "isLiked": true}, "authorId": 2, "categoriesId": [2, 11], "postType": "gallery", "galleryImgs": ["https://images.pexels.com/photos/8904068/pexels-photo-8904068.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/2082949/pexels-photo-2082949.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/1051073/pexels-photo-1051073.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/1271619/pexels-photo-1271619.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/2132126/pexels-photo-2132126.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500"]}, {"index": 810001, "id": "9f1739eb-8f00-4c99-97fsf4-63544b6a2d12", "featuredImage": "https://images.unsplash.com/photo-1504992963429-56f2d62fbff0?ixid=MnwxMjA3fDB8MHxzZWFyY2h8ODN8fHRlY2hub2xvZ3l8ZW58MHx8MHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80", "title": "Mind games: How gaming can play a positive role in mental health", "desc": "<PERSON>is bibendum, felis sed interdum venenatis, turpis enim blandit mi, in porttitor pede justo eu massa. Donec dapibus. Duis at velit eu est congue elementum.", "date": "May 20, 2021", "href": "/single-gallery/this-is-single-slug", "commentCount": 18, "viewdCount": 3800, "readingTime": 5, "bookmark": {"count": 1168, "isBookmarked": false}, "like": {"count": 1255, "isLiked": true}, "authorId": 4, "categoriesId": [4, 13], "postType": "gallery", "galleryImgs": ["https://images.pexels.com/photos/7354472/pexels-photo-7354472.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/1483024/pexels-photo-1483024.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/2101528/pexels-photo-2101528.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/2070485/pexels-photo-2070485.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/2106776/pexels-photo-2106776.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500"]}, {"index": 910002, "id": "0991ab0b-696f-4d7f-afe7-9c624fsfseb8c050", "featuredImage": "https://images.unsplash.com/photo-1465310477141-6fb93167a273?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1950&q=80", "title": "Microsoft announces a five-year commitment to create bigger opportunities for people with disabilities", "desc": "Duis bibendum. Morbi non quam nec dui luctus rutrum. Nulla tellus.", "date": "May 20, 2021", "href": "/single-gallery/this-is-single-slug", "commentCount": 19, "viewdCount": 4515, "readingTime": 3, "bookmark": {"count": 3463, "isBookmarked": true}, "like": {"count": 2586, "isLiked": false}, "authorId": 9, "categoriesId": [9, 18], "postType": "gallery", "galleryImgs": ["https://images.pexels.com/photos/7354442/pexels-photo-7354442.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/307008/pexels-photo-307008.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/753626/pexels-photo-753626.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/1118448/pexels-photo-1118448.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/372098/pexels-photo-372098.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500"]}, {"index": 1010101, "id": "eae0e85d-db11-44fa-ac32-6c192ffsf687e0c", "featuredImage": "https://images.unsplash.com/photo-1581122584612-713f89daa8eb?ixid=MnwxMjA3fDB8MHxwaG90by1yZWxhdGVkfDIwfHx8ZW58MHx8fHw%3D&ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80", "title": "360-degree video: How Microsoft deployed a datacenter to the bottom of the ocean", "desc": "We’re an online magazine dedicated to covering the best in international product design. We started as a little blog back in 2002 covering student work and over time", "date": "May 20, 2021", "href": "/single-gallery/this-is-single-slug", "commentCount": 14, "viewdCount": 2378, "readingTime": 6, "bookmark": {"count": 3502, "isBookmarked": false}, "like": {"count": 773, "isLiked": true}, "authorId": 9, "categoriesId": [9, 18], "postType": "gallery", "galleryImgs": ["https://images.pexels.com/photos/335393/pexels-photo-335393.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/2253821/pexels-photo-2253821.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/2088282/pexels-photo-2088282.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/2533092/pexels-photo-2533092.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/164287/pexels-photo-164287.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500"]}, {"index": 1110101, "id": "10f85e8a-a75a-485a-a1af-75551fsfs93fae1a", "featuredImage": "https://images.unsplash.com/photo-1606490114832-d41056bdca34?ixid=MnwxMjA3fDB8MHxzZWFyY2h8MzY1fHxnYW1pbmd8ZW58MHx8MHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80", "title": "To cool datacenter servers, Microsoft turns to boiling liquid", "desc": "Nam ultrices, libero non mattis pulvinar, nulla pede ullamcorper augue, a suscipit nulla elit ac nulla. Sed vel enim sit amet nunc viverra dapibus. Nulla suscipit ligula in lacus.", "date": "May 20, 2021", "href": "/single-gallery/this-is-single-slug", "commentCount": 39, "viewdCount": 1460, "readingTime": 3, "bookmark": {"count": 732, "isBookmarked": false}, "like": {"count": 1917, "isLiked": false}, "authorId": 3, "categoriesId": [3, 12], "postType": "gallery", "galleryImgs": ["https://images.pexels.com/photos/2147487/pexels-photo-2147487.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/416676/pexels-photo-416676.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/258136/pexels-photo-258136.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/1319829/pexels-photo-1319829.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/597049/paris-france-eiffel-tower-597049.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500"]}, {"index": 1210101, "id": "f924ccfb-2622-497d-8072-fsfsfsdf373c73cbaa", "featuredImage": "https://images.unsplash.com/photo-1587381420844-7bc5f4feec02?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80", "title": "Xbox connects people to help through Crisis Text Line", "desc": "Aenean lectus. Pellentesque eget nunc. Donec quis orci eget orci vehicula condimentum.", "date": "May 20, 2021", "href": "/single-gallery/this-is-single-slug", "commentCount": 2, "viewdCount": 3418, "readingTime": 7, "bookmark": {"count": 1502, "isBookmarked": false}, "like": {"count": 913, "isLiked": false}, "authorId": 9, "categoriesId": [9, 18], "postType": "gallery", "galleryImgs": ["https://images.pexels.com/photos/347141/pexels-photo-347141.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/3155666/pexels-photo-3155666.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/2662116/pexels-photo-2662116.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/1660995/pexels-photo-1660995.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500"]}, {"index": 13111222, "id": "6fa9d0b2-d48b-48f3-b862-cb38a4bsss6c755", "featuredImage": "https://images.unsplash.com/photo-1576500714954-8a687d0ea1ef?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80", "title": "Unusual ‘machine in the woods’ taps clean energy deep underground for new Microsoft campus", "desc": "In hac habitasse platea dictumst. Etiam faucibus cursus urna. Ut tellus.", "date": "May 20, 2021", "href": "/single-gallery/this-is-single-slug", "commentCount": 5, "viewdCount": 3894, "readingTime": 4, "bookmark": {"count": 3384, "isBookmarked": true}, "like": {"count": 1731, "isLiked": false}, "authorId": 10, "categoriesId": [10, 19], "postType": "gallery", "galleryImgs": ["https://images.pexels.com/photos/347141/pexels-photo-347141.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/3155666/pexels-photo-3155666.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/2662116/pexels-photo-2662116.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/1660995/pexels-photo-1660995.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500"]}, {"index": 1410101, "id": "95de5119-776e-4c7f-9ff3-fsfsfs52482f9a2081", "featuredImage": "https://images.unsplash.com/photo-1501785888041-af3ef285b470?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80", "title": "WNBA’s <PERSON> teams up with Xbox to empower young girls to pursue their hoop dreams ", "desc": "In hac habitasse platea dictumst. Etiam faucibus cursus urna. Ut tellus.", "date": "May 20, 2021", "href": "/single-gallery/this-is-single-slug", "commentCount": 27, "viewdCount": 1038, "readingTime": 4, "bookmark": {"count": 3818, "isBookmarked": false}, "like": {"count": 1160, "isLiked": false}, "authorId": 2, "categoriesId": [2, 11], "postType": "gallery", "galleryImgs": ["https://images.pexels.com/photos/624015/pexels-photo-624015.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/1438761/pexels-photo-1438761.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/1755243/pexels-photo-1755243.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/1643113/pexels-photo-1643113.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/3265460/pexels-photo-3265460.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500"]}, {"index": 151010101, "id": "499ea541-5d70-4978-81111b41-eeb3987024bd", "featuredImage": "https://images.unsplash.com/photo-1571435763834-4d6fbb550bb7?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80", "title": "New tools and advocacy support Black pregnant and postpartum mothers to save lives ", "desc": "In sagittis dui vel nisl. Duis ac nibh. Fusce lacus purus, aliquet at, feugiat non, pretium quis, lectus.", "date": "May 20, 2021", "href": "/single-gallery/this-is-single-slug", "commentCount": 3, "viewdCount": 584, "readingTime": 5, "bookmark": {"count": 1308, "isBookmarked": false}, "like": {"count": 2182, "isLiked": false}, "authorId": 3, "categoriesId": [3, 12], "postType": "gallery", "galleryImgs": ["https://images.pexels.com/photos/347141/pexels-photo-347141.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/3155666/pexels-photo-3155666.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/2662116/pexels-photo-2662116.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/1660995/pexels-photo-1660995.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500"]}, {"index": 16101010111, "id": "20992b33-1bae-4867-b11110fb-dec76ac1e0da", "featuredImage": "https://images.unsplash.com/photo-1596017443581-4c823c7ae6d3?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80", "title": "New Orleans Game Camp creates opportunities for a more diverse pipeline into the gaming industry ", "desc": "Quisque porta volutpat erat. Quisque erat eros, viverra eget, congue eget, semper rutrum, nulla. Nunc purus.", "date": "May 20, 2021", "href": "/single-gallery/this-is-single-slug", "commentCount": 19, "viewdCount": 52, "readingTime": 5, "bookmark": {"count": 482, "isBookmarked": true}, "like": {"count": 627, "isLiked": false}, "authorId": 9, "categoriesId": [9, 18], "postType": "gallery", "galleryImgs": ["https://images.pexels.com/photos/347141/pexels-photo-347141.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/3155666/pexels-photo-3155666.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/2662116/pexels-photo-2662116.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/1660995/pexels-photo-1660995.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500"]}, {"index": 17101011, "id": "71fde139-b111176-44e3-8514-2296f1d888d3", "featuredImage": "https://images.unsplash.com/photo-1589010588553-46e8e7c21788?ixid=MnwxMjA3fDB8MHxzZWFyY2h8NTB8fGZvb2R8ZW58MHx8MHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80", "title": "Unusual ‘machine in the woods’ taps clean energy deep underground for new Microsoft campus ", "desc": "<PERSON>sce posuere felis sed lacus. Morbi sem mauris, la<PERSON><PERSON> ut, rhoncus aliquet, pulvinar sed, nisl. Nunc rhoncus dui vel sem.", "date": "May 20, 2021", "href": "/single-gallery/this-is-single-slug", "commentCount": 1, "viewdCount": 2996, "readingTime": 2, "bookmark": {"count": 2515, "isBookmarked": false}, "like": {"count": 1277, "isLiked": false}, "authorId": 10, "categoriesId": [10, 19], "postType": "gallery", "galleryImgs": ["https://images.pexels.com/photos/347141/pexels-photo-347141.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/3155666/pexels-photo-3155666.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/2662116/pexels-photo-2662116.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/1660995/pexels-photo-1660995.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500"]}, {"index": 181101011, "id": "f63fbb28-94a6-4a87-bfe4-5111ac6a9e7990b", "featuredImage": "https://images.unsplash.com/photo-1488036106564-87ecb155bb15?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80", "title": "New ideas and energized employees fuel Microsoft’s ongoing efforts toward racial equity ", "desc": "Aenean lectus. Pellentesque eget nunc. Donec quis orci eget orci vehicula condimentum.", "date": "May 20, 2021", "href": "/single-gallery/this-is-single-slug", "commentCount": 1, "viewdCount": 129, "readingTime": 7, "bookmark": {"count": 3976, "isBookmarked": false}, "like": {"count": 3126, "isLiked": false}, "authorId": 4, "categoriesId": [4, 13], "postType": "gallery", "galleryImgs": ["https://images.pexels.com/photos/347141/pexels-photo-347141.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/3155666/pexels-photo-3155666.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/2662116/pexels-photo-2662116.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/1660995/pexels-photo-1660995.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500"]}, {"index": 19191919, "id": "2980bc99111-74c7-4507-a28d-78966ec71acf", "featuredImage": "https://images.unsplash.com/photo-1586348943529-beaae6c28db9?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80", "title": "With lessons rooted in social justice movements, Minecraft’s Good Trouble aims to help build a better world.", "desc": "<PERSON>ulla ut erat id mauris vulputate elementum. Nullam varius. Nulla facilisi.", "date": "May 20, 2021", "href": "/single-gallery/this-is-single-slug", "commentCount": 45, "viewdCount": 575, "readingTime": 7, "bookmark": {"count": 2264, "isBookmarked": true}, "like": {"count": 1642, "isLiked": false}, "authorId": 7, "categoriesId": [7, 16], "postType": "gallery", "galleryImgs": ["https://images.pexels.com/photos/347141/pexels-photo-347141.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/3155666/pexels-photo-3155666.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/2662116/pexels-photo-2662116.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/1660995/pexels-photo-1660995.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500"]}, {"index": 20100111, "id": "c8f8e679-d434-4a1111c-9448-efc44814d11e", "featuredImage": "https://images.unsplash.com/photo-1444464666168-49d633b86797?ixid=MnwxMjA3fDB8MHxzZWFyY2h8MjV8fG5hdHVyZXxlbnwwfHwwfHw%3D&ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80", "title": "Early community involvement has ‘meant a lot’ in Atlanta as Microsoft plans to expand its presence ", "desc": "Mae<PERSON>nas leo odio, condimentum id, luctus nec, molestie sed, justo. Pellentesque viverra pede ac diam. Cras pellentesque volutpat dui.", "date": "May 20, 2021", "href": "/single-gallery/this-is-single-slug", "commentCount": 36, "viewdCount": 2783, "readingTime": 4, "bookmark": {"count": 1653, "isBookmarked": true}, "like": {"count": 505, "isLiked": false}, "authorId": 10, "categoriesId": [10, 19], "postType": "gallery", "galleryImgs": ["https://images.pexels.com/photos/347141/pexels-photo-347141.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/3155666/pexels-photo-3155666.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/2662116/pexels-photo-2662116.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/1660995/pexels-photo-1660995.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500"]}, {"index": 2101010111, "id": "293b349d-5112-4eb4-a28b-5db8dbd511111aed", "featuredImage": "https://images.unsplash.com/photo-1532274402911-5a369e4c4bb5?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80", "title": "Xbox Ambassadors build community, reinforce inclusion and positive gaming ", "desc": "Morbi porttitor lorem id ligula. Suspendisse ornare consequat lectus. In est risus, auctor sed, tristique in, tempus sit amet, sem.", "date": "May 20, 2021", "href": "/single-gallery/this-is-single-slug", "commentCount": 6, "viewdCount": 979, "readingTime": 6, "bookmark": {"count": 3728, "isBookmarked": true}, "like": {"count": 1234, "isLiked": false}, "authorId": 3, "categoriesId": [3, 12], "postType": "gallery", "galleryImgs": ["https://images.pexels.com/photos/347141/pexels-photo-347141.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/3155666/pexels-photo-3155666.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/2662116/pexels-photo-2662116.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/1660995/pexels-photo-1660995.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500"]}, {"index": 2201010101, "id": "fa96a568-e4ce1111-4beb-a2ac-120a164e0e4a", "featuredImage": "https://images.unsplash.com/photo-1506197603052-3cc9c3a201bd?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80", "title": "New tools help companies pinpoint — and fix — the unexpected things that make remote work so draining ", "desc": "Curabitur in libero ut massa volutpat convallis. Morbi odio odio, elementum eu, interdum eu, tincidunt in, leo. Maecenas pulvinar lobortis est.", "date": "May 20, 2021", "href": "/single-gallery/this-is-single-slug", "commentCount": 7, "viewdCount": 1501, "readingTime": 6, "bookmark": {"count": 3278, "isBookmarked": false}, "like": {"count": 1803, "isLiked": false}, "authorId": 7, "categoriesId": [7, 16], "postType": "gallery", "galleryImgs": ["https://images.pexels.com/photos/347141/pexels-photo-347141.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/3155666/pexels-photo-3155666.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/2662116/pexels-photo-2662116.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/1660995/pexels-photo-1660995.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500"]}, {"index": 2301010011, "id": "0e48196f-644b-46d4-99f8-9ece6761218c", "featuredImage": "https://images.unsplash.com/photo-1621792907526-e69888069079?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=634&q=80", "title": "Phasellus sit amet erat. Nulla tempus. Vivamus in felis eu sapien cursus vestibulum.", "desc": "Aliquam quis turpis eget elit sodales scelerisque. <PERSON><PERSON><PERSON> sit amet eros. Suspendisse accumsan tortor quis turpis.", "date": "May 20, 2021", "href": "/single-gallery/this-is-single-slug", "commentCount": 14, "viewdCount": 3004, "readingTime": 7, "bookmark": {"count": 2090, "isBookmarked": false}, "like": {"count": 2811, "isLiked": true}, "authorId": 4, "categoriesId": [4, 13], "postType": "gallery", "galleryImgs": ["https://images.pexels.com/photos/347141/pexels-photo-347141.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/3155666/pexels-photo-3155666.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/2662116/pexels-photo-2662116.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", "https://images.pexels.com/photos/1660995/pexels-photo-1660995.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500"]}]