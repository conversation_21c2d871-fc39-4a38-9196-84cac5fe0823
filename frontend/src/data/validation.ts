import * as yup from 'yup';

export interface SignUpFormValues {
  email: string;
  name: string;
  phone: string;
  password: string;
  password_confirmation: string;
}

export interface SignInFormValues {
  email: string;
  password: string;
}

export interface ForgotPasswordFormValues {
  email: string;
}

export interface ChangePassFormValues {
  current_password: string;
  password: string;
}

export interface ResetPassFormValues {
  email: string;
  password: string;
  password_confirmation: string;
}

export interface UploadAvatarFormValues {
  avatar: File | null | string;
}

export interface UpdateProfileFormValues {
  name: string;
  phone: string;
  dob: string;
  gender: string;
  description?: string;
  email: string;
}

export const schemaRegisterUser: yup.ObjectSchema<SignUpFormValues> = yup.object({
  email: yup.string().email('Email không hợp lệ').required('Vui lòng nhập email'),
  name: yup.string().required('Vui lòng nhập tên hiển thị'),
  phone: yup.string().required('<PERSON>ui lòng nhập số điện thoại').matches(/^\d{9,11}$/, '<PERSON><PERSON> điện thoại phải là số và có từ 9 đến 11 chữ số').matches(/^\S+$/, 'Số điện thoại không được chứa khoảng trắng'),
  password: yup.string().required('Vui lòng nhập mật khẩu').min(6, 'Mật khẩu phải có ít nhất 6 ký tự').matches(/[a-z]/, 'Phải có ít nhất 1 chữ thường').matches(/[A-Z]/, 'Phải có ít nhất 1 chữ hoa').matches(/\d/, 'Phải có ít nhất 1 số').matches(/[^a-zA-Z0-9]/, 'Phải có ít nhất 1 ký tự đặc biệt'),
  password_confirmation: yup.string().required('Vui lòng xác nhận mật khẩu').oneOf([yup.ref('password')], 'Mật khẩu không khớp')
});

export const schemaLoginUser: yup.ObjectSchema<SignInFormValues> = yup.object({
  email: yup.string().email('Email không hợp lệ').required('Vui lòng nhập email'),
  password: yup.string().required('Vui lòng nhập mật khẩu')
});

export const schemaForgotPassword: yup.ObjectSchema<ForgotPasswordFormValues> = yup.object({
  email: yup.string().email('Email không hợp lệ').required('Vui lòng nhập email')
});

export const schemaUpdateProfile: yup.ObjectSchema<UpdateProfileFormValues> = yup.object({
  email: yup.string().email('Email không hợp lệ').required('Vui lòng nhập email'),
  name: yup.string().required('Vui lòng nhập tên hiển thị'),
  phone: yup.string().required('Vui lòng nhập số điện thoại').matches(/^\d{9,11}$/, 'Số điện thoại phải là số và có từ 9 đến 11 chữ số').matches(/^\S+$/, 'Số điện thoại không được chứa khoảng trắng'),
  dob: yup.string().required('Vui lòng nhập ngày sinh'),
  gender: yup.string().required('Vui lòng chọn giới tính'),
  description: yup.string().max(500, 'Mô tả không được vượt quá 200 ký tự').optional()
});

export const schemaChangePass: yup.ObjectSchema<ChangePassFormValues> = yup.object({
  current_password: yup.string().required('Vui lòng nhập mật khẩu cũ'),
    password: yup.string().required('Vui lòng nhập mật khẩu mới').min(6, 'Mật khẩu phải có ít nhất 6 ký tự').matches(/[a-z]/, 'Phải có ít nhất 1 chữ thường').matches(/[A-Z]/, 'Phải có ít nhất 1 chữ hoa').matches(/\d/, 'Phải có ít nhất 1 số').matches(/[^a-zA-Z0-9]/, 'Phải có ít nhất 1 ký tự đặc biệt')
  }
);

export const schemaResetPass: yup.ObjectSchema<ResetPassFormValues> = yup.object({
  email: yup.string().email('Email không hợp lệ').required('Vui lòng nhập email'),
  password: yup.string().required('Vui lòng nhập mật khẩu').min(6, 'Mật khẩu phải có ít nhất 6 ký tự').matches(/[a-z]/, 'Phải có ít nhất 1 chữ thường').matches(/[A-Z]/, 'Phải có ít nhất 1 chữ hoa').matches(/\d/, 'Phải có ít nhất 1 số').matches(/[^a-zA-Z0-9]/, 'Phải có ít nhất 1 ký tự đặc biệt'),
  password_confirmation: yup.string().required('Vui lòng xác nhận mật khẩu').test('passwords-match', 'Mật khẩu xác nhận không khớp', function(value) {
    return value === this.parent.password;
  })
});

export const schemaUploadAvatar: yup.ObjectSchema<UploadAvatarFormValues> = yup.object({
  avatar: yup.mixed<File>().required('Vui lòng chọn ảnh đại diện').test('fileSize', 'File ảnh phải nhỏ hơn 2MB', (file) => {
    if (!file) {
      return false;
    }
    if (file instanceof File) {
      return file.size <= 2 * 1024 * 1024;
    }
    return false;
  }).test('fileType', 'Chỉ cho phép file ảnh (jpg, png, webp)', (file) => {
    if (!file) {
      return false;
    }
    if (file instanceof File) {
      return ['image/jpeg', 'image/png', 'image/webp'].includes(file.type);
    }
    return false;
  })
});

export interface SubmitCommentFormValues {
  content: string;
}

export const schemaSubmitComment: yup.ObjectSchema<SubmitCommentFormValues> = yup.object({
  content: yup.string().min(1, 'Bình luận không được ít hơn 1 ký tự ').max(1000, 'Bình luận không được vượt quá  1000 ký tự').required('Vui lòng nhập bình luận'),
});
