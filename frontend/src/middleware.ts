import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

const protectedPaths = ['/dashboard'];
const guestOnlyPaths = ['/login', '/signup', '/forgot-password', '/password'];

export function middleware(request: NextRequest) {
  const url = request.nextUrl;
  const pathname = url.pathname;
  const searchParams = url.searchParams;

  const redirectParam = searchParams.get('redirect');
  const hashParam = searchParams.get('hash');
  const isToken = !!cookies().get('token')?.value;

  if (protectedPaths.some((path) => pathname.startsWith(path))) {
    if (!isToken) {
      return NextResponse.redirect(new URL('/login', request.url));
    }
  }
  if (guestOnlyPaths.some((path) => pathname.startsWith(path))) {
    if (isToken) {
      if (redirectParam) {
        const redirectUrl = new URL(decodeURIComponent(redirectParam), url.origin);
        if (hashParam) {
          redirectUrl.hash = `#${hashParam}`;
        }
        return NextResponse.redirect(redirectUrl);
      } else {
        return NextResponse.redirect(new URL('/', request.url));
      }
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: '/((?!_next/static|_next/image).*)'
};
