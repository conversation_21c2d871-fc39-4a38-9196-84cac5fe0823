'use client';

import { create } from 'zustand';
import { getLocalStorage, setLocalStorage } from '@/utils/localStorage';
import { RECENT_SEARCH_KEY } from '@/utils/constant';


interface RecentSearchState {
  recentSearches: string[];
  setRecentSearches: (keywords: string[]) => void;
  addRecentSearch: (keyword: string) => void;
  clearRecentSearches: () => void;
  loadRecentSearches: () => void;
}

export const useRecentSearchStore = create<RecentSearchState>((set, get) => ({
  recentSearches: [],

  setRecentSearches: (keywords) => {
    const sliced = keywords.slice(0, 5);
    setLocalStorage(RECENT_SEARCH_KEY, JSON.stringify(sliced));
    set(() => ({
      recentSearches: sliced,
    }));
  },

  addRecentSearch: (keyword) => {
    const current = get().recentSearches;
    const filtered = current.filter((k) => k !== keyword);
    const updated = [keyword, ...filtered].slice(0, 5);
    setLocalStorage(RECENT_SEARCH_KEY, JSON.stringify(updated));
    set(() => ({
      recentSearches: updated,
    }));
  },

  clearRecentSearches: () => {
    setLocalStorage(RECENT_SEARCH_KEY, JSON.stringify([]));
    set(() => ({
      recentSearches: [],
    }));
  },

  loadRecentSearches: () => {
    if (typeof window !== 'undefined') {
      const stored = getLocalStorage(RECENT_SEARCH_KEY);
      if (stored) {
        try {
          const keywords = JSON.parse(stored);
          set(() => ({
            recentSearches: Array.isArray(keywords) ? keywords.slice(0, 5) : [],
          }));
        } catch {
          set(() => ({ recentSearches: [] }));
        }
      }
    }
  }
}));
