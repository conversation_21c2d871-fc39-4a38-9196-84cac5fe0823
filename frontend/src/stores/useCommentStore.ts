'use client';

import { create } from 'zustand';
import { toast } from 'react-toastify';
import { interactionApi } from '@/apis/interactionApi';

interface CommentStore {
  content: {id: string, content: string};
  setContent: (val: {id: string, content: string}) => void;
  reset: () => void;
  createComment: (data: {id: string, content: string}) => Promise<void>;
  replyComment: (data: {id: string, idComment: string, content: string}) => Promise<void>;
  editComment: (data: {id: string, content: string}) => Promise<void>;
  deleteComment: (data: {id: string}) => Promise<void>;
  isDelete: boolean;
  setIsDelete: (val: boolean) => void;
  isEdit: boolean;
  setIsEdit: (val: boolean) => void;
  totalComment: number;
  setTotalComment: (val: number) => void;
}

export const useCommentStore = create<CommentStore>((set, get) => ( {
  content: { id: '', content: '' },
  isDelete: false,
  isEdit: false,
  totalComment: 0,
  setContent: (val) => set({ content: val }),
  setIsDelete: (val) => set({ isDelete: val }),
  setIsEdit: (val) => set({ isEdit: val }),
  setTotalComment: (val) => set({ totalComment: val }),
  reset: () => set({ content: { id: '', content: '' } }),
  createComment: async (data: {id: string, content: string}) => {
    const { reset, setContent } = get();
    try {
      const res = await interactionApi.post({
        endpoint: 'comments',
        payload: JSON.stringify({
          reference_id: data.id,
          content: data.content
        })
      });
      setContent({
        id: data.id,
        content: data.content
      });
      if (!res.error) {
        toast.success('Bình luận đã được gửi');
      } else {
        toast.error(res.error?.error?.message || 'Bình luận không thành công');
        reset();
      }
    } catch (error) {
      toast.error('Có lỗi xảy ra');
    }
  },
  replyComment: async (data: {id: string, idComment: string, content: string}) => {
    const { reset, setContent } = get();
    const { id, idComment, content } = data;

    try {
      const res = await interactionApi.post({
        endpoint:`comments/${idComment}/reply`,
        payload: JSON.stringify({
          reference_id: id,
          content: content
        })
      });
      setContent({
        id: id,
        content: content
      });
      if (!res.error) {
        toast.success('Bình luận đã được gửi');
      } else {
        toast.error(res.error?.error?.message || 'Bình luận không thành công');
        reset();
      }
    } catch (error) {
      toast.error('Có lỗi xảy ra');
    }
  },
  editComment: async (data: {id: string, content: string}) => {
    const { reset, setContent, setIsEdit } = get();
    try {
      const res = await interactionApi.put({
        endpoint: `comments/${data.id}`,
        payload: JSON.stringify({ content: data.content })
      });
      setContent({
        id: data.id,
        content: data.content
      });
      setIsEdit(true);
      if (!res.error) {
        toast.success('Thay đổi bình luận thành công');
      } else {
        toast.error(res.error?.error?.message || 'Thay đổi bình luận không thành công');
        reset();
      }
    } catch (error) {
      toast.error('Có lỗi xảy ra');
    }
  },
  deleteComment: async (data: {id: string}) => {
    const { totalComment, reset, setContent, setIsDelete, setTotalComment } = get();
    try {
      const res = await interactionApi.delete({
        endpoint: `comments/${data.id}`
      });
      if (!res.error) {
        toast.success('Xóa bình luận thành công');
        setContent({
          id: data.id,
          content: ''
        });
        setTotalComment(totalComment - 1);
        setIsDelete(true);
      } else {
        toast.error(res.error?.error?.message || 'Xóa bình luận không thành công');
        reset();
      }
    } catch (error) {
      toast.error('Có lỗi xảy ra');
    }
  }
} ));
