'use client';

import Heading2 from '@/components/Heading/Heading2';
import { Route } from '@/routers/types';
import { usePathname } from 'next/navigation';
import React, { ReactNode } from 'react';
import { authApi } from '@/apis/authApi';
import { clientClearCookie } from '@/lib/clientCookie';
import { forceReload } from '@/utils/forceReload';
import { LinkRoute } from '@/components/LinkRoute';

const subPages: {href: Route; pageName: string; emoij: string, description: string}[] = [
  {
    href: '/dashboard' as Route,
    emoij: '⏳',
    description: 'Bảng điều khiển của bạn, quản lý hồ sơ của bạn.',
    pageName: 'Bảng điều khiển'
  },
  {
    href: '/dashboard/edit-password' as Route,
    emoij: '🛠',
    description: 'Cập nhật mật khẩu của bạn',
    pageName: 'Cậ<PERSON> nhật mật khẩu'
  }
];

const DashboardContainer = ({ children }: {children: ReactNode}) => {
  const pathname = usePathname();

  const handleViewTitlePage = () => {
    const pageName = subPages.find((item) => item.href === pathname);
    if (pageName) {
      return pageName.pageName;
    }
    return '';
  };

  const handleViewDescriptionPage = () => {
    const pageName = subPages.find((item) => item.href === pathname);
    if (pageName) {
      return pageName.description;
    }
    return '';
  };

  const handleLogout = async () => {
    await authApi.post({
      endpoint: 'logout'
    }).then(() => {
      clientClearCookie().finally(() => {
        forceReload();
      });
    });
  };

  return (
    <div className={`nc-PageDashboard`}>
      <header className="text-center max-w-2xl mx-auto - mb-14 sm:mb-16 lg:mb-24">
        <Heading2 emoji="">{handleViewTitlePage()}</Heading2>
        <span className="block text-sm mt-2 text-neutral-700 sm:text-base dark:text-neutral-200">
          {handleViewDescriptionPage()}
        </span>
      </header>

      <div className="flex flex-col space-y-8 xl:space-y-0 xl:flex-row">
        {/* SIDEBAR */}

        <div className="flex-shrink-0 max-w-xl xl:w-80 xl:pe-8">
          <ul className="text-base space-y-1 text-neutral-700 dark:text-neutral-400">
            {subPages.map(({ href, pageName, emoij }, index) => {
              return (
                <li key={index}>
                  <LinkRoute
                    className={`px-6 py-3 font-medium rounded-full flex items-center ${
                      pathname === href
                        ?
                        'bg-neutral-100 dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100'
                        :
                        'hover:text-neutral-800 hover:bg-neutral-100 dark:hover:bg-neutral-800 dark:hover:text-neutral-100'
                    }`}
                    href={href}
                  >
                    <span className="w-8 me-2 text-lg">{emoij}</span>
                    <span> {pageName}</span>
                  </LinkRoute>
                </li>
              );
            })}

            <li className=" border-t border-neutral-200 dark:border-neutral-700" />
            <li>
              <LinkRoute
                className={`flex items-center px-6 py-3 font-medium text-red-500`}
                href={'/#' as Route}
                onClick={handleLogout}
              >
                <span className="w-8 me-2 text-lg">💡</span>
                {'Đăng xuất'}
              </LinkRoute>
            </li>
          </ul>
        </div>

        <div className="border-t border-neutral-500 dark:border-neutral-300 md:hidden"></div>

        <div className="flex-1">{children}</div>
      </div>
    </div>
  );
};

export default DashboardContainer;
