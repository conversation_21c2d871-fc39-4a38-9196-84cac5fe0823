import React, { FC } from 'react';
import NcImage from '@/components/NcImage/NcImage';
import PostCardMeta from '@/components/PostCardMeta/PostCardMeta';
import PostCardSaveAction from '@/components/PostCardSaveAction/PostCardSaveAction';
import PostCardLikeAndComment from '@/components/PostCardLikeAndComment/PostCardLikeAndComment';
import PostTypeFeaturedIcon from '@/components/PostTypeFeaturedIcon/PostTypeFeaturedIcon';
import { Post } from '@/contains/post';
import { IMG_PLACEHOLDER } from '@/contains/contants';
import { LinkRoute } from '@/components/LinkRoute';
import CategoryBadgeList from '@/components/CategoryBadgeList/CategoryBadgeList';
import { readingTime } from 'reading-time-estimator';

export interface Card3Props {
  className?: string;
  post: Post;
}

const Card3: FC<Card3Props> = ({ className = 'h-full', post }) => {
  const { slug, image, name, description, content } = post;
  const result = readingTime(content ?? '', 100);

  return (
    <div
      className={`nc-Card3 relative flex flex-row items-center group ${className}`}
    >
      <div className="flex flex-col flex-grow">
        <div className="space-y-3.5">
          <CategoryBadgeList categories={post.categories} />
          <LinkRoute href={`/${slug}` ?? '/'} className="block">
            <h2
              className={`nc-card-title block font-medium sm:font-semibold text-neutral-900 dark:text-neutral-100 text-sm sm:text-base xl:text-lg`}
            >
              <span className="line-clamp-2" title={name}>
                {name}
              </span>
            </h2>
            <div className="hidden sm:block sm:mt-2">
              <span className="text-neutral-500 dark:text-neutral-400 text-sm line-clamp-2">
                {description}
              </span>
            </div>
          </LinkRoute>

          <PostCardMeta meta={{ ...post }} />
        </div>
        <div className="mt-5 flex items-center flex-wrap justify-between">
          <PostCardLikeAndComment
            id={post.id}
            isCancelGetCountLike={true}
            liked={post.is_liked}
            likeCount={post.likes_count}
            comments_count={post.comments_count}
            postSlug={slug}
          />
          <PostCardSaveAction readingTime={result.minutes} hidenReadingTime={false} />
        </div>
      </div>

      <div
        className={`block flex-shrink-0 w-24 sm:w-36 md:w-44 xl:w-56 ms-3 sm:ms-6 rounded-3xl overflow-hidden z-0 mb-5 sm:mb-0`}
      >
        <LinkRoute
          href={`/${slug}` ?? '/'}
          className="block w-full h-0 aspect-h-1 aspect-w-1 relative"
        >
          <NcImage
            containerClassName="absolute inset-0"
            src={image ?? IMG_PLACEHOLDER}
            fill
            alt={name}
          />

          <span>
            <PostTypeFeaturedIcon
              className="absolute left-2 bottom-2"
              postType={'standard'}
              wrapSize="w-8 h-8"
              iconSize="w-4 h-4"
            />
          </span>
        </LinkRoute>
      </div>
    </div>
  );
};

export default Card3;
