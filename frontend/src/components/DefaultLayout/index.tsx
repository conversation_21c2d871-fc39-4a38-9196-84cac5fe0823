import React, { lazy, Suspense } from 'react';
import LoadingEffect from '../LoadingEffect';

const SectionAds = lazy(() => import('@/components/Sections/SectionAds'));
const SectionBecomeAnAuthor = lazy(() => import('@/components/SectionBecomeAnAuthor/SectionBecomeAnAuthor'));
const CategoryTrendingWarper = lazy(() => import('@/components/Home/CategoryTrendingWarper').then(m => ( { default: m.CategoryTrendingWarper } )));
const BannerWarper = lazy(() => import('@/components/Home/BannerWarper').then(m => ( { default: m.BannerWarper } )));
const AuthorWarper = lazy(() => import('@/components/Home/AuthorWarper').then(m => ( { default: m.AuthorWarper } )));
const MostCommentWarper = lazy(() => import('@/components/Home/MostCommentWarper').then(m => ( { default: m.MostCommentWarper } )));
const MostScoreWarper = lazy(() => import('@/components/Home/MostScoreWarper').then(m => ( { default: m.MostScoreWarper } )));
const MostFeaturedCategoriesWarper = lazy(() => import('@/components/Home/MostFeaturedCategoriesWarper').then(m => ( { default: m.MostFeaturedCategoriesWarper } )));
const NewestPostWarper = lazy(() => import('@/components/Home/NewestPostWarper').then(m => ( { default: m.NewestPostWarper } )));
const GridPostWarper = lazy(() => import('@/components/Home/GridPostWarper').then(m => ( { default: m.GridPostWarper } )));

interface IDefaultLayoutProps {
  searchParams: {[key: string]: string | undefined};
}

const DefaultLayout = ({
  searchParams
}: IDefaultLayoutProps) => {
  return (
    <div className="nc-PageHome relative">
      <Suspense fallback={<LoadingEffect />}>
        <BannerWarper />
      </Suspense>

      <Suspense fallback={<LoadingEffect />}>
        <AuthorWarper />
      </Suspense>

      <Suspense fallback={<LoadingEffect />}>
        <CategoryTrendingWarper />
      </Suspense>

      <Suspense fallback={<LoadingEffect />}>
        <MostCommentWarper />
      </Suspense>

      <Suspense fallback={<LoadingEffect />}>
        <SectionAds className="pb-16 lg:pb-28" />
      </Suspense>

      <Suspense fallback={<LoadingEffect />}>
        <GridPostWarper />
      </Suspense>

      <Suspense fallback={<LoadingEffect />}>
        <SectionBecomeAnAuthor />
      </Suspense>

      <Suspense fallback={<LoadingEffect />}>
        <MostFeaturedCategoriesWarper />
      </Suspense>


      <Suspense fallback={<LoadingEffect />}>
        <MostScoreWarper />
      </Suspense>


      <Suspense fallback={<LoadingEffect />}>
        <NewestPostWarper searchParams={searchParams} className="py-16 lg:py-28" />
      </Suspense>
    </div>
  );
};

export default DefaultLayout;
