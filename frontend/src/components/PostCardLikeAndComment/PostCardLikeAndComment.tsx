import React, { FC } from 'react';
import PostCardCommentBtn from '@/components/PostCardCommentBtn/PostCardCommentBtn';
import PostCardLikeAction from '@/components/PostCardLikeAction/PostCardLikeAction';
import { Route } from '@/routers/types';

export interface PostCardLikeAndCommentProps {
  className?: string;
  itemClass?: string;
  hiddenCommentOnMobile?: boolean;
  useOnSinglePage?: boolean;
  id: number;
  likeCount: string;
  isCancelGetCountLike: boolean;
  comments_count?: number;
  postSlug?: Route;
  liked?: boolean;
}

const PostCardLikeAndComment: FC<PostCardLikeAndCommentProps> = ({
  className = '',
  itemClass = 'px-3 h-8 text-xs',
  hiddenCommentOnMobile = true,
  useOnSinglePage = false,
  comments_count,
  postSlug = '/',
  id,
  likeCount = '0',
  isCancelGetCountLike = true,
  liked = false
}) => {
  return (
    <div
      className={`nc-PostCardLikeAndComment flex items-center space-x-2 rtl:space-x-reverse ${className}`}
    >
      <PostCardLikeAction
        className={itemClass}
        id={id}
        likeCount={likeCount}
        isCancelGetCountLike={isCancelGetCountLike}
        liked={liked}
      />
      <PostCardCommentBtn
        className={`${
          hiddenCommentOnMobile ? 'hidden sm:flex' : 'flex'
        }  ${itemClass}`}
        isATagOnSingle={useOnSinglePage}
        comments_count={comments_count}
        postSlug={postSlug}
      />
    </div>
  );
};

export default PostCardLikeAndComment;
