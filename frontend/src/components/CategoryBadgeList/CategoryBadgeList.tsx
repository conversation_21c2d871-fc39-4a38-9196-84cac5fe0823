import React, { FC } from 'react';
import Badge from '@/components/Badge/Badge';
import { TCategory } from '@/contains/category';

export interface CategoryBadgeListProps {
  className?: string;
  itemClass?: string;
  categories: TCategory[];
}

const CategoryBadgeList: FC<CategoryBadgeListProps> = ({
  className = "flex flex-wrap gap-2",
  itemClass,
  categories,
}) => {
  return (
    <div
      className={`nc-CategoryBadgeList ${className}`}
      data-nc-id="CategoryBadgeList"
    >
      {categories.slice(0, 3).map((item, index) => (
        <Badge
          className={itemClass}
          key={index}
          name={item.name}
          href={item.slug}
          color={item.color}
        />
      ))}
      {/*{categories.length > 3 && (*/}
      {/*  <BadgeCounter*/}
      {/*    className={itemClass}*/}
      {/*    name={`+${categories.length - 3} more`}*/}
      {/*    count={categories.length - 3}*/}
      {/*  />*/}
      {/*)}*/}
    </div>
  );
};

export default CategoryBadgeList;
