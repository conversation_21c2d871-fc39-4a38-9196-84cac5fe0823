'use client';

import React, { FC, useEffect, useState } from 'react';
import { useReaction } from '@/hooks/useReaction';
import { toast } from 'react-toastify';
import { useUserServer } from '@/hooks/useUser';

export interface PostCardLikeActionProps {
  className?: string;
  likeCount: string;
  liked?: boolean;
  id: number | string;
  isCancelGetCountLike: boolean;
}

const PostCardLikeAction: FC<PostCardLikeActionProps> = ({
  className = 'px-3 h-8 text-xs',
  likeCount,
  liked = false,
  id,
  isCancelGetCountLike
}) => {
  const [isLiked, setIsLiked] = useState(liked);
  const [countLike, setCountLike] = useState<string>(likeCount);
  const { handleReaction, handleGetReaction } = useReaction({ postId: id });
  const { data: user } = useUserServer();

  useEffect(() => {
    if (!id || isCancelGetCountLike) {
      return;
    } else {
      handleGetReaction().then((res) => {
        setCountLike(res ?? '0');
      });
    }

  }, [id, isCancelGetCountLike]);

  const handleLike = async () => {
    if (!user?.data?.id) {
      return toast.error('Vui lòng đăng nhập để thực hiện hành động này');
    }
    try {
      const res = await handleReaction();
      if (!!res) {
        setCountLike(res);
        const newIsLiked = !isLiked;
        setIsLiked(newIsLiked);
      } else {
        toast.error('Địa chỉ email chưa được xác thực');
      }
    } catch (error) {
      console.error('Error handling reaction:', error);
      setIsLiked((prev) => !prev);
    }
  };
  return (
    <button
      className={`nc-PostCardLikeAction relative min-w-[68px] flex items-center rounded-full leading-none group transition-colors ${className} ${
        isLiked
          ?
          'text-rose-600 bg-rose-50 dark:bg-rose-100'
          :
          'text-neutral-700 bg-neutral-50 dark:text-neutral-200 dark:bg-neutral-800 hover:bg-rose-50 dark:hover:bg-rose-100 hover:text-rose-600 dark:hover:text-rose-500'
      }`}
      onClick={handleLike}
      title="Liked"
    >
      <svg
        width="24"
        height="24"
        fill={isLiked ? 'currentColor' : 'none'}
        viewBox="0 0 24 24"
      >
        <path
          fillRule="evenodd"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1"
          d="M11.995 7.23319C10.5455 5.60999 8.12832 5.17335 6.31215 6.65972C4.49599 8.14609 4.2403 10.6312 5.66654 12.3892L11.995 18.25L18.3235 12.3892C19.7498 10.6312 19.5253 8.13046 17.6779 6.65972C15.8305 5.18899 13.4446 5.60999 11.995 7.23319Z"
          clipRule="evenodd"
        ></path>
      </svg>

      {countLike && (
        <span
          className={`ml-1 ${
            isLiked ? 'text-rose-600' : 'text-neutral-900 dark:text-neutral-200'
          }`}
        >
          {countLike}
        </span>
      )}
    </button>
  );
};

export default PostCardLikeAction;
