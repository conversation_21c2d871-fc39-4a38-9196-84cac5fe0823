import React, { <PERSON> } from "react";
import Image from "next/image";
import { Post } from '@/contains/post';
import { IMG_PLACEHOLDER } from '@/contains/contants';

export interface PostFeaturedMediaProps {
  className?: string;
  post: Post;
  isHover?: boolean;
}

const PostFeaturedMedia: FC<PostFeaturedMediaProps> = ({
  className = "w-full h-full",
  post,
  isHover = false,
}) => {
  const { image,  id, slug } = post;



  return (
    <div className={`nc-PostFeaturedMedia relative ${className}`}>
        <Image
          alt="featured"
          fill
          className="object-cover"
          src={image ?? IMG_PLACEHOLDER}
          sizes="(max-width: 600px) 480px, 800px"
        />

      {/*<GallerySlider*/}
      {/*  href={slug}*/}
      {/*  galleryImgs={galleryImgs}*/}
      {/*  className="absolute inset-0 z-10"*/}
      {/*  galleryClass="absolute inset-0"*/}
      {/*  ratioClass="absolute inset-0"*/}
      {/*/>*/}
    </div>
  );
};

export default PostFeaturedMedia;
