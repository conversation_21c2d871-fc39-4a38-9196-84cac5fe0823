'use client';

import { useThemeOptions } from '@/hooks/useTheme';
import './style.scss';

const LoadingEffect = () => {
  const { data: theme } = useThemeOptions();
  const primaryColor = theme?.general.primary_color || '#3cefff';
  return (
    <div id="loadingEffect" className="container p-20 text-center w-full min-h-[480px] flex items-center justify-center">
      <div className="relative min-h-[480px]">
        <div
          className="outer" style={{
          borderTopColor: primaryColor,
          borderRightColor: primaryColor
        }}
        />
        <div
          className="middle" style={{
          borderTopColor: primaryColor,
          borderRightColor: primaryColor
        }}
        />
        <div
          className="inner" style={{
          borderTopColor: primaryColor,
          borderRightColor: primaryColor
        }}
        />
      </div>
    </div>
  );
};

export default LoadingEffect;
