'use client';

import React, { useTransition } from 'react';
import { Post } from '@/contains/post';
import Card11 from '@/components/Card11/Card11';
import Pagination from '@/components/Pagination/Pagination';
import { ApiResponseMeta } from '@/contains/author';
import { useRouter, useSearchParams } from 'next/navigation';
import { Route } from '@/routers/types';
import Card11Skeleton from '@/components/Card11/Card11Skeleton';
import { FiltersClient } from '@/components/FiltersClient';
import Nodata from '@/components/Nodata';

export default function ListPostAuthor({ posts }: {
  posts: {
    data: Post[];
    meta?: ApiResponseMeta;
  };
}) {
  const [isPending, startTransition] = useTransition();
  const router = useRouter();
  const searchParams = useSearchParams();

  const current_page = posts?.meta?.current_page ?? 1;
  const total = posts?.meta?.total ?? 0;
  const per_page = 10;

  const handleChangePage = (page: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('page', String(page));
    params.set('per_page', String(per_page));
    const newUrl = `?${params.toString()}` as Route;

    router.push(newUrl);

    startTransition(() => {});
  };

  return (
    <main>
      {/* FILTER HEADER */}
      <div className="flex flex-col sm:items-center sm:justify-end sm:flex-row">
        <div className="block my-4 border-b w-full border-neutral-300 dark:border-neutral-500 sm:hidden"></div>
        <FiltersClient />
      </div>

      {/* LOADING UI */}
      {isPending ? (
        <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8 mt-8 lg:mt-10">
          {Array(10).fill(0).map((_, index) => (
            <Card11Skeleton key={index} className='min-h-[297px]'/>
          ))}
        </div>
      ) : (
        <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8 mt-8 lg:mt-10">
          {posts?.data?.map((post) => (
            <Card11 key={post.id} post={post} className='min-h-[297px]'/>
          ))}
        </div>
      )}
      {posts.data.length===0 && <Nodata />}
      {/* PAGINATION */}
      <div className="flex flex-col mt-12 lg:mt-16 space-y-5 sm:space-y-0 sm:space-x-3 sm:flex-row sm:justify-between sm:items-center">
        <Pagination
          perPage={per_page}
          total={total}
          currentPage={current_page}
          onPageChange={handleChangePage}
        />
      </div>
    </main>
  );
}
