"use client";

import React, { FC } from 'react';
import Heading from '@/components/Heading/Heading';
import MySlider from '../MySlider';
import { TCategory } from '@/contains/category';
import CardCategory4 from '@/components/CardCategory4/CardCategory4';
import CardCategory3 from '@/components/CardCategory3/CardCategory3';

export interface SectionSliderNewCategoriesProps {
  className?: string;
  heading: string;
  subHeading: string;
  categories: TCategory[];
  itemPerRow?: 4 | 5;
  categoryCardType?: "card3" | "card4";
}

const SectionSliderNewCategories: FC<SectionSliderNewCategoriesProps> = ({
  heading,
  subHeading,
  className = "",
  categories,
  itemPerRow = 5,
  categoryCardType,
}) => {
  const renderCard = (item: TCategory, index: number) => {
    const topIndex = index < 3 ? `#${index + 1}` : undefined;
    switch (categoryCardType) {
      case "card3":
        return <CardCategory3 key={index} category={item} />;
      case "card4":
        return <CardCategory4  key={index} index={topIndex} category={item}/>;
      default:
        return null;
    }
  };

  return (
    <div className={`nc-SectionSliderNewCategories ${className}`}>
      <Heading desc={subHeading}>{heading}</Heading>
      <MySlider
        data={categories}
        renderItem={(item, index) => renderCard(item, index)}
        itemPerRow={itemPerRow}
      />
    </div>
  );
};

export default SectionSliderNewCategories;
