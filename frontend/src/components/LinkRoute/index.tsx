'use client';

import Link from 'next/link';
import React, { CSSProperties, HTMLAttributeAnchorTarget, memo } from 'react';
import { Route } from '@/routers/types';

interface ILinkRoute {
  href: string | any;
  children?: React.ReactNode;
  className?: string;
  scroll?: boolean;
  onClick?: () => void;
  target?: HTMLAttributeAnchorTarget;
  rel?: string;
  title?: string;
  replace?: boolean;
  prefetch?: boolean;
  style?: CSSProperties;
}

export const LinkRoute = memo(({
  href,
  children,
  className = '',
  scroll = true,
  onClick,
  target,
  rel,
  title,
  replace = false,
  prefetch,
  style,
}: ILinkRoute) => {
  const prefetchValue = process.env.PREFETCH_PAGE === 'true' ?? prefetch;
  return (
    <Link
      href={href as Route}
      scroll={scroll}
      replace={replace}
      prefetch={prefetchValue}
      className={className}
      onClick={onClick}
      target={target}
      rel={rel}
      title={title}
      style={style}
    >
      {children}
    </Link>
  );
});

LinkRoute.displayName = 'LinkRoute';
