import React from 'react';
import { FiltersClient } from '@/components/FiltersClient';
import SectionSliderNewAuthors from '@/components/SectionSliderNewAthors/SectionSliderNewAuthors';

export const MainSection = ({ children, modal }: {children: React.ReactNode, modal: React.ReactNode}) => (
  <div className="container pt-10 pb-16 lg:pb-28 lg:pt-20 space-y-16 lg:space-y-28">
    <div>
      <div className="flex flex-col sm:justify-between sm:flex-row">
        <div className="flex space-x-2.5 rtl:space-x-reverse">
          {modal}
        </div>
        <div className="block my-4 border-b w-full border-neutral-300 dark:border-neutral-500 sm:hidden"></div>
        <FiltersClient />
      </div>
      {children}
    </div>

    <SectionSliderNewAuthors
      heading="Top elite authors"
      subHeading="Discover our elite writers"
    />
  </div>
);
