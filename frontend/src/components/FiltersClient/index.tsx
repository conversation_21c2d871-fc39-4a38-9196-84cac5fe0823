'use client';

import ArchiveFilterListBox from '@/components/ArchiveFilterListBox/ArchiveFilterListBox';
import { FILTERS } from '@/contains/types';
import React from 'react';
import { useRouter, useSearchParams } from 'next/navigation';

export const FiltersClient = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const params = new URLSearchParams(searchParams.toString());
  const onChangeSort = (data: {name: string; value: string}) => {
    const value = data.value;

    params.delete('order');
    params.delete('order_by');

    if (value === 'asc' || value === 'desc') {
      params.set('order', value);
    } else {
      params.set('order_by', value);
    }

    router.push(`?${params.toString()}`, { scroll: false });
  };
  return (
    <div className="flex justify-end">
      <ArchiveFilterListBox lists={FILTERS} onChange={onChangeSort}/>
    </div>
  );
};
