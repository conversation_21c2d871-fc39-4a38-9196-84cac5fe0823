import React, { <PERSON> } from 'react';
import Image from 'next/image';
import { Post } from '@/contains/post';
import { LinkRoute } from '@/components/LinkRoute';
import PostCardMeta from '@/components/PostCardMeta/PostCardMeta';

export interface Card3SmallProps {
  className?: string;
  post: Post;
}

const Card3Small: FC<Card3SmallProps> = ({ className = 'h-full', post }) => {
  const { name, slug, image } = post;

  return (
    <div
      className={`nc-Card3Small relative flex flex-row justify-between items-center ${className}`}
    >
      <LinkRoute href={`/${slug}` ?? '/'} className="absolute inset-0" title={name}></LinkRoute>
      <div className="relative space-y-2">
        <PostCardMeta meta={{ ...post }} />
        <h2 className="nc-card-title block text-sm sm:text-base font-medium sm:font-semibold text-neutral-900 dark:text-neutral-100">
          <LinkRoute href={`/${slug}` ?? '/'}  className="line-clamp-2" title={name}>
            {name}
          </LinkRoute>
        </h2>
      </div>

      <LinkRoute
        href={`/${slug}` ?? '/'}
        title={name}
        className={`block w-20 flex-shrink-0 relative rounded-lg overflow-hidden z-0 ms-4 group`}
      >
        <div className={`w-full h-0 aspect-w-1 aspect-h-1`}>
          {image ? <Image
            alt="featured"
            sizes="100px"
            className="object-cover w-full h-full group-hover:scale-110 transform transition-transform duration-300"
            src={image}
            fill
            title={name}
          /> : <></>}

        </div>
      </LinkRoute>
    </div>
  );
};

export default Card3Small;
