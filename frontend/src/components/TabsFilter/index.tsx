'use client';
import Nav from '@/components/Nav/Nav';
import NavItem from '@/components/NavItem/NavItem';
import ArchiveFilterListBox from '@/components/ArchiveFilterListBox/ArchiveFilterListBox';
import React, { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { FILTERS, TABS } from '@/contains/types';

interface Props {
  activeTab: string;
}

export const TabsFilter = ({ ...props }: Props) => {
  const { activeTab } = props;
  const router = useRouter();
  const searchParams = useSearchParams();
  const params = new URLSearchParams(searchParams.toString());
  const tab = searchParams.get('tab') ?? '';
  const [tabActive, setTabActive] = useState(tab);

  useEffect(() => {
    if (!!activeTab) {
      setTabActive(activeTab);
    }

  }, [activeTab]);

  const handleClickTab = (item: string) => {
    if (item === tabActive) {
      return;
    }
    params.delete('order');
    params.delete('order_by');
    params.set('tab', item);
    setTabActive(item);
    router.push(`?${params.toString()}`, { scroll: false });
  };

  const handleConvertValueSort = (value: string) => {
    // score, views, comments_count
    switch (value) {
      case 'total_score':
        return 'score';
      case 'total_comments':
        return 'comments_count';
      case 'total_views':
        return 'views';
      default:
        return '';
    }
  };

  const onChangeSort = (data: {name: string; value: string}) => {
    const value = data.value;

    params.delete('order');
    params.delete('order_by');

    if (value === 'asc' || value === 'desc') {
      params.set('order', value);
    } else {
      if (tab === 'post') {
        const convertValue = handleConvertValueSort(value);
        params.set('order_by', convertValue);
      } else {
        params.set('order_by', value);
      }
    }

    router.push(`?${params.toString()}`, { scroll: false });
  };

  return (
    <div className="flex flex-col sm:items-center sm:justify-between sm:flex-row">
      <Nav
        containerClassName="w-full overflow-x-auto hiddenScrollbar"
        className="sm:space-x-2 rtl:space-x-reverse"
      >
        {TABS.map((item, index) => (
          <NavItem
            isActive={item.value === tabActive}
            key={index}
            onClick={() => handleClickTab(item.value)}
          >
            {item.name.charAt(0).toUpperCase() + item.name.slice(1)}
          </NavItem>
        ))}
      </Nav>
      <div className="block my-4 border-b w-full border-neutral-300 dark:border-neutral-500 sm:hidden"></div>
      <div className="flex justify-end">
        <ArchiveFilterListBox lists={FILTERS} onChange={onChangeSort} />
      </div>
    </div>
  );
};
