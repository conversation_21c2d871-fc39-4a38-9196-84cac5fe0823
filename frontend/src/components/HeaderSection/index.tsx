import Image from 'next/image';
import React from 'react';

export const HeaderSection = ({ title, subTitle, count, image }: {
  title: string;
  subTitle: string,
  count: number,
  image?: string
}) => (
  <div className="w-full px-2 xl:max-w-screen-2xl mx-auto">
    <div className="relative aspect-w-16 aspect-h-13 sm:aspect-h-9 lg:aspect-h-8 xl:aspect-h-5 rounded-3xl md:rounded-[40px] overflow-hidden z-0">
      <Image
        alt="archive"
        fill
        src={image || 'https://images.pexels.com/photos/2662116/pexels-photo-2662116.jpeg?auto=compress&cs=tinysrgb&dpr=2&h=750&w=1260'}
        className="object-cover w-full h-full rounded-3xl md:rounded-[40px]"
        sizes="(max-width: 1280px) 100vw, 1536px"
      />
      <div className="absolute inset-0 bg-black text-white bg-opacity-30 flex flex-col items-center justify-center">
        <h2 className="inline-block align-middle text-5xl font-semibold md:text-7xl">{title}</h2>
        <span className="block mt-4 text-neutral-300">{!!count ? count : ''} {subTitle}</span>
      </div>
    </div>
  </div>
);
