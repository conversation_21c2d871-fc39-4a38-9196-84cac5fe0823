'use client';
import { useAdminNavigationStore } from '@/stores/useAdminNavigationStore';
import { useEffect } from 'react';
import { useLocation } from 'react-use';

interface ValidateIDProps {
  postId?: number;
  userId?: number;
}

export const ValidateID = ({
  postId,
  userId
}: ValidateIDProps) => {
  const { setActiveId, reset } = useAdminNavigationStore();
  const location = useLocation();
  const { pathname } = location;
  useEffect(() => {
    if (postId) {
      setActiveId('post', postId);
    }
    if (userId) {
      setActiveId('user', userId);
    }
    if (!postId && !userId) {
      reset();
    }
  }, [postId, userId, pathname]);
  return null;
};
