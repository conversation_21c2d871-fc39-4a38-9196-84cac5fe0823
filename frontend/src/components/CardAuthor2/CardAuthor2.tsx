import React, { FC } from 'react';
import Avatar from '@/components/Avatar/Avatar';
import { LinkRoute } from '@/components/LinkRoute';
import { TAuthor } from '@/contains/author';
import { IMG_PLACEHOLDER } from '@/contains/contants';
import { DateFormat } from '@/components/DateFormat';

export interface CardAuthor2Props {
  author: TAuthor;
  className?: string;
  readingTime?: number;
  date: string;
  hoverReadingTime?: boolean;
}

const CardAuthor2: FC<CardAuthor2Props> = ({
  className = "",
  author,
  readingTime,
  date,
  hoverReadingTime = true
}) => {
  const { image } = author;
  const fullName: string = `${author?.first_name ?? ''} ${author?.last_name ?? ''}`.trim() || 'Author';
  return (
    <LinkRoute
      href={author.username ? `/author/${author.username}` : '/'}
      className={`nc-CardAuthor2 relative inline-flex items-center group ${className}`}
    >
      <Avatar
        sizeClass="h-10 w-10 text-base"
        containerClassName="flex-shrink-0 me-3"
        radius="rounded-full"
        imgUrl={image ?? IMG_PLACEHOLDER}
        userName={fullName}
      />
      <div>
        <h2
          className={`text-sm text-neutral-700 hover:text-black dark:text-neutral-300 dark:hover:text-white font-medium`}
        >
          {fullName}
        </h2>
        <span
          className={`flex items-center mt-1 text-xs text-neutral-500 dark:text-neutral-400`}
        >
          <span>
            <DateFormat date={date}/>
          </span>
          {!!readingTime && (
            <>
              <span
                className={`hidden lg:inline mx-1 transition-opacity ${
                  hoverReadingTime ? 'opacity-0 group-hover:opacity-100' : ''
                }`}
              >
                ·
              </span>
              <span
                className={`hidden lg:inline transition-opacity ${
                  hoverReadingTime ? 'opacity-0 group-hover:opacity-100' : ''
                }`}
              >
                {readingTime} Phút đọc
              </span>
            </>
          )}
        </span>
      </div>
    </LinkRoute>
  );
};

export default CardAuthor2;
