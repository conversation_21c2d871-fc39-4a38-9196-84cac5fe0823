import React, { FC } from "react";
import MainNav2Logged from "./MainNav2Logged";
import { IMenuResponse, IThemeOptions } from '@/contains/types';

export interface HeaderLoggedProps {
  themeData: IThemeOptions | null;
  menuData: IMenuResponse | null
}

const HeaderLogged: FC<HeaderLoggedProps> = ({...props}) => {
  const { themeData, menuData } = props;
  return (
    <div className="nc-HeaderLogged sticky top-0 w-full z-40">
      <MainNav2Logged themeData={themeData} menuData={menuData}/>
    </div>
  );
};

export default HeaderLogged;
