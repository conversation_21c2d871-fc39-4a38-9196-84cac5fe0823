'use client';

import React, { FC, Fragment, ReactNode, useEffect, useState } from 'react';
import { Combobox, Dialog, Transition } from '@headlessui/react';
import { ClockIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import { useRouter } from 'next/navigation';
import { LinkRoute } from '@/components/LinkRoute';
import { useRecentSearchStore } from '@/stores/useRecentSearchStore';

function classNames(...classes: any) {
  return classes.filter(Boolean).join(' ');
}

interface Props {
  renderTrigger?: () => ReactNode;
}

const SearchModal: FC<Props> = ({ renderTrigger }) => {
  const [open, setOpen] = useState(false);
  const [rawQuery, setRawQuery] = useState('');
  const { recentSearches, addRecentSearch, loadRecentSearches } = useRecentSearchStore();

  const router = useRouter();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const keyword = rawQuery.trim();
    if (keyword) {
      addRecentSearch(keyword);
    }
    router.push(`/search?search=${encodeURIComponent(keyword)}`);
    setOpen(false);
  };

  const handleClickKeyword = (keyword: string) => {
    addRecentSearch(keyword);
    router.push(`/search?search=${encodeURIComponent(keyword)}`);
    setOpen(false);
  };

  const filteredRecentSearches = rawQuery
    ? recentSearches.filter((keyword) => keyword.toLowerCase().includes(rawQuery.toLowerCase()))
    : recentSearches;

  useEffect(() => {
    loadRecentSearches();
  }, []);

  return (
    <>
      <div onClick={() => setOpen(true)} className="cursor-pointer">
        {renderTrigger ? renderTrigger() : (
          <button className="flex w-10 h-10 sm:w-12 sm:h-12 rounded-full text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-800 focus:outline-none items-center justify-center">
            <MagnifyingGlassIcon className="h-6 w-6" />
          </button>
        )}
      </div>

      <Transition.Root show={open} as={Fragment} afterLeave={() => setRawQuery('')} appear>
        <Dialog as="div" className="relative z-[99]" onClose={() => setOpen(false)}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/40 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-0 z-10 overflow-y-auto p-4 sm:p-6 md:p-20">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-100"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-100"
            >
              <Dialog.Panel
                className="block mx-auto max-w-2xl transform divide-y divide-gray-100 overflow-hidden rounded-xl bg-white shadow-2xl ring-1 ring-black ring-opacity-5 transition-all"
                as="form"
                onSubmit={handleSubmit}
              >
                <Combobox value={rawQuery} onChange={(value: string) => handleClickKeyword(value)} name="searchpallet">
                  <div className="relative">
                    <MagnifyingGlassIcon
                      className="pointer-events-none absolute top-3.5 left-4 h-5 w-5 text-gray-400"
                      aria-hidden="true"
                    />
                    <Combobox.Input
                      className="h-12 w-full border-0 bg-transparent pl-11 pr-4 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm"
                      placeholder="Search..."
                      onChange={(event) => setRawQuery(event.target.value)}
                    />
                  </div>
                  {filteredRecentSearches.length > 0 && (
                    <Combobox.Options
                      static
                      className="max-h-80 scroll-py-10 scroll-pb-2 space-y-4 overflow-y-auto p-4 pb-2"
                    >
                      {filteredRecentSearches.length > 0 && (
                        <li>
                          <ul className="-mx-4 text-sm text-gray-700">
                            {filteredRecentSearches.map((search, index) => (
                              <Combobox.Option
                                key={index}
                                value={search}
                                className={({ active }) =>
                                  classNames(
                                    'flex select-none items-center px-4 py-2 cursor-pointer',
                                    active && 'bg-indigo-600 text-white'
                                  )
                                }
                              >
                                {({ active }) => (
                                  <>
                                    <ClockIcon
                                      className={classNames(
                                        'h-6 w-6 flex-none',
                                        active ? 'text-white' : 'text-gray-400'
                                      )}
                                      aria-hidden="true"
                                    />
                                    <span className="ms-3 flex-auto truncate">
                                      {search}
                                    </span>
                                  </>
                                )}
                              </Combobox.Option>
                            ))}
                          </ul>
                        </li>
                      )}

                      {/*{filteredProjects.length > 0 && (*/}
                      {/*  <li>*/}
                      {/*    <h2 className="text-xs font-semibold text-gray-900">*/}
                      {/*      Categories*/}
                      {/*    </h2>*/}
                      {/*    <ul className="-mx-4 mt-2 text-sm text-gray-700">*/}
                      {/*      {filteredProjects.map((project) => (*/}
                      {/*        <Combobox.Option*/}
                      {/*          key={project.id}*/}
                      {/*          value={project}*/}
                      {/*          className={({ active }) =>*/}
                      {/*            classNames(*/}
                      {/*              "flex select-none items-center px-4 py-2",*/}
                      {/*              active && "bg-indigo-600 text-white"*/}
                      {/*            )*/}
                      {/*          }*/}
                      {/*        >*/}
                      {/*          {({ active }) => (*/}
                      {/*            <>*/}
                      {/*              <HashtagIcon*/}
                      {/*                className={classNames(*/}
                      {/*                  "h-6 w-6 flex-none",*/}
                      {/*                  active ? "text-white" : "text-gray-400"*/}
                      {/*                )}*/}
                      {/*                aria-hidden="true"*/}
                      {/*              />*/}
                      {/*              <span className="ms-3 flex-auto truncate">*/}
                      {/*                {project.name}*/}
                      {/*              </span>*/}
                      {/*            </>*/}
                      {/*          )}*/}
                      {/*        </Combobox.Option>*/}
                      {/*      ))}*/}
                      {/*    </ul>*/}
                      {/*  </li>*/}
                      {/*)}*/}

                      {/*{filteredUsers.length > 0 && (*/}
                      {/*  <li>*/}
                      {/*    <h2 className="text-xs font-semibold text-gray-900">*/}
                      {/*      Authors*/}
                      {/*    </h2>*/}
                      {/*    <ul className="-mx-4 mt-2 text-sm text-gray-700">*/}
                      {/*      {filteredUsers.map((user) => (*/}
                      {/*        <Combobox.Option*/}
                      {/*          key={user.id}*/}
                      {/*          value={user}*/}
                      {/*          className={({ active }) =>*/}
                      {/*            classNames(*/}
                      {/*              "flex select-none items-center px-4 py-2",*/}
                      {/*              active && "bg-indigo-600 text-white"*/}
                      {/*            )*/}
                      {/*          }*/}
                      {/*        >*/}
                      {/*          <Image*/}
                      {/*            src={user.avatar}*/}
                      {/*            alt="author"*/}
                      {/*            className="h-6 w-6 flex-none rounded-full"*/}
                      {/*            sizes="30px"*/}
                      {/*          />*/}
                      {/*          <span className="ms-3 flex-auto truncate">*/}
                      {/*            {user.displayName}*/}
                      {/*          </span>*/}
                      {/*        </Combobox.Option>*/}
                      {/*      ))}*/}
                      {/*    </ul>*/}
                      {/*  </li>*/}
                      {/*)}*/}
                    </Combobox.Options>
                  )}

                  {/*{rawQuery === "?" && (*/}
                  {/*  <div className="py-14 px-6 text-center text-sm sm:px-14">*/}
                  {/*    <LifebuoyIcon*/}
                  {/*      className="mx-auto h-6 w-6 text-gray-400"*/}
                  {/*      aria-hidden="true"*/}
                  {/*    />*/}
                  {/*    <p className="mt-4 font-semibold text-gray-900">*/}
                  {/*      Help with searching*/}
                  {/*    </p>*/}
                  {/*    <p className="mt-2 text-gray-500">*/}
                  {/*      Use this tool to quickly search for users and projects*/}
                  {/*      across our entire platform. You can also use the search*/}
                  {/*      modifiers found in the footer below to limit the results*/}
                  {/*      to just users or projects.*/}
                  {/*    </p>*/}
                  {/*  </div>*/}
                  {/*)}*/}

                  <div className="flex flex-wrap items-center bg-gray-50 py-2.5 px-4 text-xs text-gray-700">
                    {/*Type{" "}*/}
                    {/*<kbd*/}
                    {/*  className={classNames(*/}
                    {/*    "mx-1 flex h-5 w-5 items-center justify-center rounded border bg-white font-semibold sm:mx-2",*/}
                    {/*    rawQuery.startsWith("#")*/}
                    {/*      ? "border-indigo-600 text-indigo-600"*/}
                    {/*      : "border-gray-400 text-gray-900"*/}
                    {/*  )}*/}
                    {/*>*/}
                    {/*  #*/}
                    {/*</kbd>{" "}*/}
                    {/*<span className="sm:hidden">for projects,</span>*/}
                    {/*<span className="hidden sm:inline">*/}
                    {/*  to access projects,*/}
                    {/*</span>*/}
                    {/*<kbd*/}
                    {/*  className={classNames(*/}
                    {/*    "mx-1 flex h-5 w-5 items-center justify-center rounded border bg-white font-semibold sm:mx-2",*/}
                    {/*    rawQuery.startsWith(">")*/}
                    {/*      ? "border-indigo-600 text-indigo-600"*/}
                    {/*      : "border-gray-400 text-gray-900"*/}
                    {/*  )}*/}
                    {/*>*/}
                    {/*  &gt;*/}
                    {/*</kbd>{" "}*/}
                    {/*for users,{" "}*/}
                    {/*<kbd*/}
                    {/*  className={classNames(*/}
                    {/*    "mx-1 flex h-5 w-5 items-center justify-center rounded border bg-white font-semibold sm:mx-2",*/}
                    {/*    rawQuery === "?"*/}
                    {/*      ? "border-indigo-600 text-indigo-600"*/}
                    {/*      : "border-gray-400 text-gray-900"*/}
                    {/*  )}*/}
                    {/*>*/}
                    {/*  ?*/}
                    {/*</kbd>{" "}*/}
                    {/*for help, or{" "}*/}
                    <LinkRoute
                      href={'/search'}
                      className="mx-1 flex h-5 px-1.5 items-center justify-center rounded border bg-white sm:mx-2 border-primary-6000 text-neutral-900"
                      onClick={() => setOpen(false)}
                    >
                      {'Đi đến trang tìm kiếm'}
                    </LinkRoute>{' '}
                  </div>
                </Combobox>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </Dialog>
      </Transition.Root>
    </>
  );
};

export default SearchModal;
