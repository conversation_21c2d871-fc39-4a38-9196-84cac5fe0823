import React, { FC } from 'react';
import rightImgDemo from '@/images/BecomeAnAuthorImg.png';
import ButtonPrimary from '@/components/Button/ButtonPrimary';
import Image, { StaticImageData } from 'next/image';
import BackgroundSection from '@/components/BackgroundSection/BackgroundSection';

export interface SectionBecomeAnAuthorProps {
  className?: string;
  rightImg?: string | StaticImageData;
  heading?: string;
}

const SectionBecomeAnAuthor: FC<SectionBecomeAnAuthorProps> = ({
  className = '',
  rightImg = rightImgDemo,
  heading
}) => {
  return (
    <div className="container">
      <div className="relative my-16 lg:my-24">
        <BackgroundSection />
        <div className={`nc-SectionBecomeAnAuthor relative flex flex-col lg:flex-row items-center ${className}`}>
          <div className="flex-shrink-0 mb-14 lg:mb-0 lg:mr-10 lg:w-2/5">
            <h2 className="font-semibold text-3xl sm:text-4xl mt-3">
              {heading}
            </h2>
            <span className="block mt-8 text-neutral-500 dark:text-neutral-400">
              Trở thành một tác giả, bạn có thể viết bài và chia sẻ những góc nhìn mới về hầu hết mọi chủ đề về anime và manga. Mọi người đều được chào đón.
            </span>
            <ButtonPrimary className="mt-8">Trở thành một tác giả</ButtonPrimary>
          </div>
          <div className="flex-grow">
            <Image
              alt="hero"
              sizes="(max-width: 768px) 100vw, 50vw"
              src={rightImg}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default SectionBecomeAnAuthor;
