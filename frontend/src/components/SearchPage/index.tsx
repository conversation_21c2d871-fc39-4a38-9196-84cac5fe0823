'use client';

import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import Pagination from '@/components/Pagination/Pagination';
import NcImage from '@/components/NcImage/NcImage';
import NcLink from '@/components/NcLink/NcLink';
import BackgroundSection from '@/components/BackgroundSection/BackgroundSection';
import Card11 from '@/components/Card11/Card11';
import CardCategory2 from '@/components/CardCategory2/CardCategory2';
import Tag from '@/components/Tag/Tag';
import { TabsFilter } from '@/components/TabsFilter';
import { categoryApi } from '@/apis/categoryApi';
import { tagApi } from '@/apis/tagApi';
import { TCategory } from '@/contains/category';
import { TTag } from '@/contains/tag';
import SectionGridCategoryBox from '@/components/SectionGridCategoryBox/SectionGridCategoryBox';
import SectionSliderNewAuthors from '@/components/SectionSliderNewAthors/SectionSliderNewAuthors';
import ButtonSecondary from '@/components/Button/ButtonSecondary';
import { Route } from '@/routers/types';
import { SearchContainer } from '@/components/SearchContainer';
import LoadingEffect from '../LoadingEffect';
import { Post } from '@/contains/post';
import { postApi } from '@/apis/postApi';
import { ENDPOINT } from '@/contains/endpoint';
import { ITEM_PER_PAGE_16 } from '@/utils/constant';
import { useRecentSearchStore } from '@/stores/useRecentSearchStore';
import Nodata from '@/components/Nodata';
import { getCookie } from 'cookies-next/client';

const SearchPage = () => {
  const searchParams = useSearchParams();
  const activeTab = searchParams.get('tab') ?? 'post';
  const page = searchParams.get('page') ?? '1';
  const search = searchParams.get('search') ?? '';
  const order = searchParams.get('order') ?? undefined;
  const orderBy = searchParams.get('order_by') ?? undefined;

  const [loading, setLoading] = useState(false);
  const [listPosts, setListPosts] = useState<Post[]>([]);
  const [listCategories, setListCategories] = useState<TCategory[]>([]);
  const [listTags, setListTags] = useState<TTag[]>([]);
  const [limitCategories, setLimitCategories] = useState<TCategory[]>([]);
  const [metaTotal, setMetaTotal] = useState(0);

  const { recentSearches, loadRecentSearches } = useRecentSearchStore();

  useEffect(() => {
    loadRecentSearches();
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);

      if (activeTab === 'post') {
        const token = getCookie('token');
        const res = await postApi.get({
          endpoint: ENDPOINT.GET_POSTS_FILTERS_SCORE,
          params: {
            page, per_page: ITEM_PER_PAGE_16,
            ...( search && { search } ),
            ...( order && { order } ),
            ...( orderBy && { order_by: orderBy } )
          },
          headers: token ? { Authorization: `Bearer ${token}` } : undefined
        });
        setListPosts(res.data || []);
        setMetaTotal(res.meta?.total ?? 0);
      }

      if (activeTab === 'categories') {
        const res = await categoryApi.get({
          endpoint: ENDPOINT.GET_CATEGORIES_FILTERS_WITH_SCORE,
          params: {
            page, per_page: ITEM_PER_PAGE_16,
            ...( search && { search } ),
            ...( order && { order } ),
            ...( orderBy && { order_by: orderBy } )
          }
        });
        setListCategories(res.data || []);
        setMetaTotal(res.meta?.total ?? 0);
      }

      if (activeTab === 'tags') {
        const res = await tagApi.get({
          endpoint: ENDPOINT.GET_TAGS_FILTERS_WITH_SCORE,
          params: {
            page, per_page: ITEM_PER_PAGE_16,
            ...( search && { search } ),
            ...( order && { order } ),
            ...( orderBy && { order_by: orderBy } )
          }
        });
        setListTags(res.data || []);
        setMetaTotal(res.meta?.total ?? 0);
      }
    };

    fetchData().finally(() => {
      setLoading(false);
    });
  }, [activeTab, page, search, order, orderBy]);

  useEffect(() => {
    const fetchLimitCategories = async () => {
      const res = await categoryApi.get<TCategory[]>({
        params: { page: 1, per_page: 15, order_by: 'created_at' }
      });
      setLimitCategories(res.data || []);
    };

    fetchLimitCategories().finally();
  }, []);

  const handleViewCount = () =>
    ['post', 'categories', 'tags', 'authors'].includes(activeTab) ? metaTotal : 0;

  return (
    <div className="nc-PageSearch">
      {/* HEADER */}
      <div className="w-screen px-2 xl:max-w-screen-2xl mx-auto">
        <div className="rounded-3xl md:rounded-[40px] relative aspect-w-16 aspect-h-9 lg:aspect-h-5 overflow-hidden z-0">
          <NcImage
            alt="search"
            fill
            containerClassName="absolute inset-0"
            src="https://images.pexels.com/photos/2138922/pexels-photo-2138922.jpeg?auto=compress&cs=tinysrgb&dpr=2&h=750&w=1260"
            className="object-cover w-full h-full"
            sizes="(max-width: 1280px) 100vw, 1536px"
          />
        </div>

        {/* CONTENT */}
        <div className="relative container -mt-20 lg:-mt-48">
          <div className="bg-white dark:bg-neutral-900 dark:border dark:border-neutral-700 p-5 lg:p-16 rounded-[40px] shadow-2xl flex items-center">
            <header className="w-full max-w-3xl mx-auto text-center flex flex-col items-center">
              <h2 className="text-2xl sm:text-4xl font-semibold">{search}</h2>
              <span className="block text-xs sm:text-sm mt-4 text-neutral-500 dark:text-neutral-300">
                We found{' '}
                <strong className="font-medium text-neutral-800 dark:text-neutral-100">
                  {handleViewCount()}
                </strong>{' '}
                results for{' '}
                <strong className="font-medium text-neutral-800 dark:text-neutral-100">
                  {search}
                </strong>
              </span>

              {/* Search Container */}
              <SearchContainer />

              {/* Related keywords */}
              {recentSearches.length > 0 && (
                <div className="w-full text-sm text-start mt-4 text-neutral-500 dark:text-neutral-300">
                  <div className="inline-block space-x-1.5 sm:space-x-2.5 rtl:space-x-reverse">
                    <span>Related:</span>
                    {recentSearches.map((keyword, idx) => (
                      <NcLink
                        key={idx}
                        className="inline-block font-normal"
                        href={`/search?search=${encodeURIComponent(keyword)}`}
                      >
                        {keyword}
                      </NcLink>
                    ))}
                  </div>
                </div>
              )}
            </header>
          </div>
        </div>
      </div>

      {/* CONTENT BODY */}
      <div className="container py-16 lg:pb-28 lg:pt-20 space-y-16 lg:space-y-28">
        <main>
          <TabsFilter activeTab={activeTab} />

          {loading ? (
            <LoadingEffect />
          ) : (
            <>
              {activeTab === 'post' && (
                <>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-5 md:gap-8 mt-8 lg:mt-10">
                    {listPosts.map((post) => (
                      <Card11 key={post.id} post={post} />
                    ))}

                  </div>
                  {listPosts.length === 0 && <Nodata />}
                </>
              )}

              {activeTab === 'categories' && (
                <>
                  <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-5 md:gap-8 mt-8 lg:mt-10">
                    {listCategories.map((cat) => (
                      <CardCategory2 key={cat.id} category={cat} />
                    ))}
                  </div>
                  {listPosts.length === 0 && <Nodata />}
                </>
              )}

              {activeTab === 'tags' && (
                <>
                  <div className="flex flex-wrap mt-12">
                    {listTags.map((tag) => (
                      <Tag className="mb-3 mr-3" key={tag.id} tag={tag} />
                    ))}
                  </div>
                  {listPosts.length === 0 && <Nodata />}
                </>
              )}
            </>
          )}

          {/* PAGINATION */}
          <div className="flex flex-col mt-12 lg:mt-16 space-y-5 sm:space-y-0 sm:space-x-3 sm:flex-row sm:justify-between sm:items-center">
            <Pagination />
          </div>
        </main>

        {/* MORE SECTIONS */}
        {/* === SECTION 5 === */}
        <div className="relative py-16">
          <BackgroundSection />
          <SectionGridCategoryBox categories={limitCategories} />
          <div className="text-center mx-auto mt-10 md:mt-16">
            <ButtonSecondary href={'/category' as Route}>Show me more</ButtonSecondary>
          </div>
        </div>

        {/* === SECTION 5 === */}
        <SectionSliderNewAuthors
          heading="Các tác giả nổi bật hàng đầu"
          subHeading="Top 5 tác giả nổi bật nhất"
        />

        {/* SUBCRIBES */}
        {/*<SectionSubscribe2 />*/}
      </div>
    </div>
  );
};

export default SearchPage;
