import CardCategory2 from '@/components/CardCategory2/CardCategory2';
import Heading from '@/components/Heading/Heading';
import React from 'react';
import { TCategory } from '@/contains/category';

export interface SectionGridCategoryBoxProps {
  categories?: TCategory[];
  headingCenter?: boolean;
  className?: string;
}

const SectionGridCategoryBox: React.FC<SectionGridCategoryBoxProps> = ({
  categories = [],
  headingCenter = true,
  className = "",
}) => {
  return (
    <div className={`nc-SectionGridCategoryBox relative ${className}`}>
      <Heading desc={`Khám phá ${categories.length} chủ đề nổi bật nhất`} isCenter={headingCenter}>
        Danh mục nhiều lượt xem
      </Heading>
      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 sm:gap-6 md:gap-8">
        {categories.map((item, i) => (
          <CardCategory2
            key={item.id}
            category={item}
          />
        ))}
      </div>
    </div>
  );
};

export default SectionGridCategoryBox;
