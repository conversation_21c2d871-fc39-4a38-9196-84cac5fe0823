import CardCategory1 from '@/components/CardCategory1/CardCategory1';
import WidgetHeading1 from '@/components/WidgetHeading1/WidgetHeading1';
import { categoryApi } from '@/apis/categoryApi';
import { TCategory } from '@/contains/category';
import { Route } from '@/routers/types';
import { ENDPOINT } from '@/contains/endpoint';
import Nodata from '@/components/Nodata';

export interface WidgetCategoriesProps {
  className?: string;
}

async function fetchCategories() {
  try {
    const { data, error } = await categoryApi.get<TCategory[]>({
      endpoint: ENDPOINT.GET_CATEGORIES_FILTERS_WITH_SCORE,
      params: {
        order_by: 'total_score',
        page: 1,
        per_page: 5,
      }
    });

    return !error ? data ?? [] : [];
  } catch (err) {
    console.error('Failed to fetch categories', err);
    return [];
  }
}

const WidgetCategories = async ({ className = 'bg-neutral-100 dark:bg-neutral-800' }: WidgetCategoriesProps) => {
  const categories = await fetchCategories();

  return (
    <div className={`nc-WidgetCategories rounded-3xl overflow-hidden ${className}`}>
      <WidgetHeading1
        title="✨ Danh mục nổi bật"
        viewAll={{ label: 'Xem tất cả', href: '/category' as Route }}
      />
      <div className="flow-root">
        <div className="flex flex-col divide-y divide-neutral-200 dark:divide-neutral-700">
          {categories.length > 0 ? (
            categories.map((category) => (
              <CardCategory1
                key={category.id}
                categories={category}
                size="normal"
                className="p-4 xl:p-5 hover:bg-neutral-200 dark:hover:bg-neutral-700"
              />
            ))
          ) : (
            <Nodata />
          )}
        </div>
      </div>
    </div>
  );
};

export default WidgetCategories;
