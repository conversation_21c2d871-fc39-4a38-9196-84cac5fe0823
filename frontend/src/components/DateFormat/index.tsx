'use client';
import { formatDate } from '@/utils/dateTime';
import React, { useState, useEffect } from 'react';
import { useThemeOptions } from '@/hooks/useTheme';

interface IDateFormatProps {
  date: string;
}

export const DateFormat: React.FC<IDateFormatProps> = ({ ...props }: IDateFormatProps) => {
  const { data: theme } = useThemeOptions();
  const { date } = props;
  const [formattedDate, setFormattedDate] = useState<string>('-');

  useEffect(() => {
    setFormattedDate(formatDate({
      dateString: date,
      formatType: theme?.general.date_format
    }));
  }, [date, theme?.general.date_format]);

  return formattedDate;
};
