import React, { FC } from 'react';
import NcImage from '@/components/NcImage/NcImage';
import { TCategory } from '@/contains/category';
import { IMG_PLACEHOLDER } from '@/contains/contants';
import Badge from '@/components/Badge/Badge';
import { LinkRoute } from '@/components/LinkRoute';

export interface CardCategory2Props {
  className?: string;
  category: TCategory;
}

const CardCategory2: FC<CardCategory2Props> = ({
  className = '',
  category
}) => {
  const name = category?.name || '';
  const slug = category?.slug || '';
  const posts_count = category?.posts_count ?? 0;
  const color = category?.color || '';

  return (
    <LinkRoute
      href={`/category/${slug}`}
      className={`nc-CardCategory2 relative flex flex-col items-center justify-center text-center px-3 py-5 sm:p-6 bg-white dark:bg-neutral-900 rounded-3xl transition-colors ${className}`}
    >
      <Badge
        color={color}
        name={'Category'}
        className="absolute -top-2 sm:top-3 left-3"
      />

      <NcImage
        containerClassName={`relative flex-shrink-0 w-20 h-20 rounded-full shadow-lg overflow-hidden z-0`}
        src={IMG_PLACEHOLDER}
        fill
        sizes="80px"
        alt="categories"
        className="object-cover "
      />
      <div className="mt-3">
        <h2 className={`text-base font-semibold`}>{name}</h2>
        <span
          className={`block mt-1 text-sm text-neutral-500 dark:text-neutral-400`}
        >
          {posts_count} Bài viết
        </span>
      </div>
    </LinkRoute>
  );
};

export default CardCategory2;
