'use client';

import React, { FC, useEffect, useState } from 'react';
import Heading from '@/components/Heading/Heading';
import CardAuthorBox2 from '@/components/CardAuthorBox2/CardAuthorBox2';
import MySlider from '@/components/MySlider';
import { TAuthor } from '@/contains/author';
import { authorApi } from '@/apis/authorApi';

export interface SectionSliderNewAuthorsProps {
  className?: string;
  heading: string;
  subHeading: string;
  authors?: TAuthor[];
  itemPerRow?: number;
}

const SectionSliderNewAuthors: <AUTHORS>
  heading = 'Suggestions for discovery',
  subHeading = 'Popular places to recommends for you',
  className = '',
  authors = [],
  itemPerRow = 4
}) => {
  const [limitAuthors, setLimitAuthors] = useState<TAuthor[]>([]);
  const isDesktop = typeof window !== 'undefined' && window.innerWidth > 1024;
  useEffect(() => {
    if (authors && authors.length > 0) {
      setLimitAuthors(authors);
    } else {
      const getLimitAuthors = async () => {
        try {
          return await authorApi.get<TAuthor[]>({
            params: {
              page: 1,
              per_page: 5,
              order_by: 'created_at'
            }
          });
        } catch (error) {
          console.error('Error fetching authors:', error);
        }
      };

      getLimitAuthors().then((res) => setLimitAuthors(res?.data ?? []));
    }

  }, []);
  return (
    <div className={`nc-SectionSliderNewAuthors ${className}`}>
      <Heading desc={subHeading} isCenter>
        {heading}
      </Heading>
      <MySlider
        itemPerRow={itemPerRow}
        data={limitAuthors}
        renderItem={(item, index) => (
          <CardAuthorBox2 key={index} author={item} />
        )}
        isDisabledSlider={limitAuthors.length <= itemPerRow && isDesktop}
      />
    </div>
  );
};

export default SectionSliderNewAuthors;
