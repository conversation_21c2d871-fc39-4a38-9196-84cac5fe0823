'use client';

import React from 'react';
import ButtonClose from '@/components/ButtonClose/ButtonClose';
import Logo from '@/components/Logo/Logo';
import { Disclosure } from '@/app/headlessui';
import { ChevronDownIcon } from '@heroicons/react/24/solid';
import { LinkRoute } from '@/components/LinkRoute';
import NcLink from '@/components/NcLink/NcLink';
import { Route } from '@/routers/types';
import { useMenus, useThemeOptions } from '@/hooks/useTheme';
import { INavItemType } from '@/contains/types';
import { convertMenusToNavItems } from '@/utils/convertMenu';

export interface NavMobileProps {
  data?: INavItemType[];
  onClickClose?: () => void;
}

const NavMobile: React.FC<NavMobileProps> = ({
  onClickClose
}) => {
  const { data: theme } = useThemeOptions();
  const { data: menu } = useMenus();
  const _renderMenuChild = (
    item: INavItemType,
    itemClass = ' pl-3 text-neutral-900 dark:text-neutral-200 font-medium '
  ) => {
    return (
      <ul className="nav-mobile-sub-menu ps-6 pb-1 text-base">
        {item.children?.map((i, index) => (
          <Disclosure key={index} as="li">
            <LinkRoute
              href={{
                pathname: i.href || undefined
              }}
              className={`flex text-sm rounded-lg hover:bg-neutral-100 dark:hover:bg-neutral-800 mt-0.5 pe-4 ${itemClass}`}
            >
              <span
                className={`py-2.5 ${!i.children ? 'block w-full' : ''}`}
                onClick={onClickClose}
              >
                {i.name}
              </span>
              {i.children && (
                <span
                  className="flex items-center flex-grow"
                  onClick={(e) => e.preventDefault()}
                >
                  <Disclosure.Button
                    as="span"
                    className="flex justify-end flex-grow"
                  >
                    <ChevronDownIcon
                      className="ms-2 h-4 w-4 text-slate-500"
                      aria-hidden="true"
                    />
                  </Disclosure.Button>
                </span>
              )}
            </LinkRoute>
            {i.children && (
              <Disclosure.Panel>
                {_renderMenuChild(
                  i,
                  'ps-3 text-slate-600 dark:text-slate-400 '
                )}
              </Disclosure.Panel>
            )}
          </Disclosure>
        ))}
      </ul>
    );
  };

  const _renderItem = (item: INavItemType, index: number) => {
    return (
      <Disclosure
        key={index}
        as="li"
        className="text-slate-900 dark:text-white"
      >
        <LinkRoute
          className="flex w-full items-center py-2.5 px-4 font-medium uppercase tracking-wide text-sm hover:bg-slate-100 dark:hover:bg-slate-800 rounded-lg"
          href={{
            pathname: item.href || undefined
          }}
        >
          <span
            className={!item.children ? 'block w-full' : ''}
            onClick={onClickClose}
          >
            {item.name}
          </span>
          {item.children && (
            <span
              className="block flex-grow"
              onClick={(e) => e.preventDefault()}
            >
              <Disclosure.Button
                as="span"
                className="flex justify-end flex-grow"
              >
                <ChevronDownIcon
                  className="ms-2 h-4 w-4 text-neutral-500"
                  aria-hidden="true"
                />
              </Disclosure.Button>
            </span>
          )}
        </LinkRoute>
        {item.children && (
          <Disclosure.Panel>{_renderMenuChild(item)}</Disclosure.Panel>
        )}
      </Disclosure>
    );
  };

  return (
    <div className="overflow-y-auto w-full h-screen py-2 transition transform shadow-lg ring-1 dark:ring-neutral-700 bg-white dark:bg-neutral-900 divide-y-2 divide-neutral-100 dark:divide-neutral-800">
      <div className="py-6 px-5">
        <div className="flex items-center justify-between pt-4">
          <Logo img={theme?.logo.logo ?? ''} height={theme?.logo.logo_height} />
        </div>

        <div className="flex flex-col mt-5 text-slate-600 dark:text-slate-300 text-sm">
          <span>
            Khám phá những bài viết nổi bật nhất trong mọi chủ đề về anime và manga.
          </span>
        </div>
        <span className="absolute end-2 top-2 p-1">
          <ButtonClose onClick={onClickClose} />
        </span>

      </div>
      <ul className="flex flex-col py-6 px-2 space-y-1 rtl:space-x-reverse">
        {convertMenusToNavItems(menu ?? []).map(_renderItem)}
      </ul>
      <div className="flex items-center justify-center py-6 px-5 space-x-2 rtl:space-x-reverse">
        <span className="block text-center text-neutral-700 dark:text-neutral-300">
          <NcLink href={'/login' as Route}>Đăng nhập</NcLink>{` / `}
          <NcLink href={'/signup' as Route}>Đăng ký</NcLink>
        </span>
      </div>
    </div>
  );
};

export default NavMobile;
