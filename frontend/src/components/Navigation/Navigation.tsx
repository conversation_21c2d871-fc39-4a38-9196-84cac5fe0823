import React, { FC } from 'react';
import NavigationItem from './NavigationItem';
import { NAVIGATION_HOME } from '@/data/navigation';
import { IMenuItem, INavItemType } from '@/contains/types';
import { convertMenusToNavItems } from '@/utils/convertMenu';

interface Props {
  className?: string;
  menuItem?: IMenuItem[];
}

const Navigation: FC<Props> = ({ className = 'flex', menuItem }) => {
  if (menuItem) {
    return (
      <ul className={`nc-Navigation items-center ${className}`}>
        {convertMenusToNavItems(menuItem).map((item) => (
          <NavigationItem key={item.id} menuItem={item} />
        ))}
      </ul>
    );
  }
  return (
    <ul className={`nc-Navigation items-center ${className}`}>
      {NAVIGATION_HOME.map((item) => (
        <NavigationItem key={item.id} menuItem={item} />
      ))}
    </ul>
  );
};

export default Navigation;
