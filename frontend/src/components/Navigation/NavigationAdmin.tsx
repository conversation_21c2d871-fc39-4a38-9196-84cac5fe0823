'use client';

import React, { FC } from 'react';
import NavigationItem from './NavigationItem';
import { useThemeOptions } from '@/hooks/useTheme';
import { INavItemType } from '@/contains/types';
import { LinkRoute } from '@/components/LinkRoute';
import { useAdminNavigationStore } from '@/stores/useAdminNavigationStore';
import { ChevronUpIcon } from '@heroicons/react/24/solid';

interface Props {
  className?: string;
  menu: INavItemType[];
  postId?: number;
}

const NavigationAdmin: FC<Props> = ({
  className = 'flex',
  menu,
  postId
}) => {
  const { data: theme } = useThemeOptions();
  const { isCollapsed, toggleCollapse } = useAdminNavigationStore();

  const [isHiding, setIsHiding] = React.useState(false);

  const handleToggleCollapse = () => {
    if (!isCollapsed) {
      setIsHiding(true);
      setTimeout(() => {
        toggleCollapse();
        setIsHiding(false);
      }, 500);
    } else {
      toggleCollapse();
    }
  };

  return (
    <div className={`nc-NavigationAdmin-container ${isCollapsed ? 'collapsed' : ''}`}>
      {/*{isCollapsed && (*/}
      {/*  <div className="fixed top-4 sm:top-24 left-16 sm:right-4 sm:left-auto sticky-show-button">*/}
      {/*    <button*/}
      {/*      onClick={handleToggleCollapse}*/}
      {/*      className="group bg-white dark:bg-gray-800 shadow-lg rounded-full p-3 hover:shadow-xl transition-all duration-300 ease-in-out focus:outline-none border border-gray-200 dark:border-gray-600 hover:scale-105"*/}
      {/*      aria-label="Menu quản trị"*/}
      {/*      aria-expanded={false}*/}
      {/*      style={{*/}
      {/*        backgroundColor: '#278143',*/}
      {/*        borderColor: theme?.general.primary_color ? `${theme.general.primary_color}40` : undefined*/}
      {/*      }}*/}
      {/*    >*/}
      {/*      <div className="flex items-center space-x-2">*/}
      {/*        <div className="p-1 rounded-full" style={{ backgroundColor: '#278143' }}>*/}
      {/*          <ChevronUpIcon className="h-4 w-4 text-white transform rotate-180" />*/}
      {/*        </div>*/}
      {/*        <span className="hidden sm:inline text-sm font-medium text-gray-700 dark:text-gray-300">*/}
      {/*         Menu quản trị*/}
      {/*        </span>*/}
      {/*      </div>*/}
      {/*    </button>*/}
      {/*  </div>*/}
      {/*)}*/}

      {(!isCollapsed || isHiding) && (
        <div className={`relative ${isHiding ? 'toolbar-slide-up' : 'toolbar-slide-down'}`}>
          <div className="relative">
            <button
              onClick={handleToggleCollapse}
              className="group relative w-full flex items-center justify-center h-10 transition-all duration-300 ease-in-out hover:shadow-md"
              style={{
                backgroundColor: '#278143',
                borderBottom: '1px solid rgba(0,0,0,0.1)'
              }}
              aria-label="Ẩn menu quản trị"
              aria-expanded={true}
            >
              <div className="absolute inset-0 bg-black opacity-0 group-hover:opacity-10 transition-opacity duration-200"></div>

              <div className="relative flex items-center space-x-3">
                <div className="transform transition-transform duration-300 ease-in-out">
                  <ChevronUpIcon className="h-5 w-5 text-white" />
                </div>

                <span className="hidden md:inline text-sm font-medium text-white transition-opacity duration-200 group-hover:opacity-90">
                  Ẩn menu quản trị
                </span>

                <span className="md:hidden text-xs font-medium text-white transition-opacity duration-200 group-hover:opacity-90">
                  Ẩn
                </span>

              </div>
            </button>
          </div>

          <div className="relative overflow-visible">
            <div
              className="absolute top-0 left-1/2 transform -translate-x-1/2 w-16 h-0.5 bg-black opacity-20 rounded-full"
            ></div>

            <ul
              className={`nc-Navigation-admin items-center justify-center h-14 ${className} relative`}
              style={{
                backgroundColor: '#278143',
                zIndex: 50
              }}
            >
              {menu.map((item, index) => (
                <li
                  key={item.id}
                  className="transform transition-all duration-300 ease-in-out scale-100 opacity-100"
                  style={{
                    transitionDelay: `${index * 50}ms`
                  }}
                >
                  <NavigationItem menuItem={item} />
                </li>
              ))}

              {postId ? (
                <li
                  className="menu-item flex-shrink-0 menu-megamenu menu-megamenu--large transform transition-all duration-300 ease-in-out scale-100 opacity-100"
                  style={{
                    transitionDelay: `${menu.length * 50}ms`
                  }}
                >
                  <LinkRoute
                    href={`/admin/blog/posts/edit/${postId}`}
                    className="inline-flex items-center text-sm font-medium py-2.5 px-4 rounded-lg hover:bg-black hover:bg-opacity-10 transition-all duration-200 group"
                  >
                    <p className="font-medium text-slate-900 dark:text-neutral-200 group-hover:scale-105 transition-transform duration-200">
                      Chỉnh sửa bài viết
                    </p>
                  </LinkRoute>
                </li>
              ) : <></>}
            </ul>
          </div>
        </div>
      )}
    </div>
  );
};

export default NavigationAdmin;
