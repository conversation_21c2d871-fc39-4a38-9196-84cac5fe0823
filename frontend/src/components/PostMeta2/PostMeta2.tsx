import React, { FC } from 'react';
import Avatar from '@/components/Avatar/Avatar';
import { Post } from '@/contains/post';
import { readingTime } from 'reading-time-estimator';
import { LinkRoute } from '@/components/LinkRoute';
import { DateFormat } from '@/components/DateFormat';

export interface PostMeta2Props {
  className?: string;
  hiddenCategories?: boolean;
  size?: 'large' | 'normal';
  avatarRounded?: string;
  post: Post;
}

const PostMeta2: FC<PostMeta2Props> = ({
  className = 'leading-none',
  hiddenCategories = false,
  size = 'normal',
  avatarRounded,
  post
}) => {
  const { author, categories, updated_at, content } = post;
  const fullName: string = `${author?.first_name ?? ''} ${author?.last_name ?? ''}`.trim() || 'Author';

  const result = readingTime(content ?? '', 100);

  return (
    <div
      className={`nc-PostMeta2 flex items-start flex-wrap text-neutral-700 text-left dark:text-neutral-200 ${
        size === 'normal' ? 'text-xs' : 'text-sm'
      } ${className}`}
    >
      <LinkRoute
        href={author.username ? `/author/${author.username}` : '/'}
        className="flex items-center space-x-2 rtl:space-x-reverse"
      >
        <Avatar
          radius={avatarRounded}
          sizeClass={
            size === 'normal'
              ? 'h-6 w-6 text-sm'
              : 'h-10 w-10 sm:h-11 sm:w-11 text-xl'
          }
          imgUrl={author?.image ?? ''}
          userName={fullName}
        />
      </LinkRoute>
      <div className="ms-3">
        <div className="flex items-center">
          <LinkRoute
            href={author.username ? `/author/${author.username}` : '/'}
            className="block font-semibold"
          >
            {fullName}
          </LinkRoute>

          {!hiddenCategories && (
            <>
              <span className="mx-2 font-semibold">·</span>
              <div className="ms-0">
                <span className="text-neutral-700 dark:text-neutral-300"><DateFormat date={updated_at} /></span>
              </div>
            </>
          )}
          <span className="mx-2 font-semibold">·</span>
          <span className="text-neutral-700 dark:text-neutral-300">
            {result.minutes} Phút đọc
          </span>
        </div>
        <div className="text-xs mt-[6px] flex flex-wrap max-w-[768px]">

        </div>
        <div className="text-xs mt-[6px] flex flex-wrap max-w-[768px]">
          <span className="text-xs">🏷 </span>
          <span className="mx-2 font-semibold">·</span>
          {categories?.map((cat, index) => (
            <LinkRoute key={cat.id} href={`/category/${cat.slug}`} className="font-semibold">
              {cat.name}
              {index < categories.length - 1 && <span>,&nbsp;</span>}
            </LinkRoute>
          ))}
        </div>
      </div>
    </div>
  );
};

export default PostMeta2;
