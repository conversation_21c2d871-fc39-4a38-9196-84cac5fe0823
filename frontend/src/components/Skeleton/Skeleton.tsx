import React, { FC } from 'react';

export interface SkeletonProps {
  className?: string;
}

const Skeleton: FC<SkeletonProps> = ({ className = '' }) => {
  return (
    <span
      className={`nc-Skeleton bg-neutral-400 inline-flex ${className}`}
      data-nc-id="Skeleton"
    ></span>
  );
};

export default Skeleton;

export function ImagePlaceholderSkeleton() {
  return (
    <div className="flex animate-pulse items-center p-4 gap-5">
      <div className="w-full">
        <h1 className="mb-4 h-3 w-36 rounded-full bg-gray-300">&nbsp;</h1>
        <div className="mb-2 h-2 w-45 rounded-full bg-gray-300">&nbsp;</div>
        <div className="mb-2 h-2 w-45 rounded-full bg-gray-300">&nbsp;</div>
      </div>
      <div className="grid min-h-[80px] min-w-[80px] place-items-center rounded-lg bg-gray-300">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          strokeWidth={2}
          stroke="currentColor"
          className="h-12 w-12 text-gray-500"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"
          />
        </svg>
      </div>
    </div>
  );
}


export function ImagePlaceholderCategorySkeleton() {
  return (
    <div className="flex animate-pulse items-center p-4 xl:p-5 hover:bg-neutral-200 dark:hover:bg-neutral-700">
      <div className="relative flex-shrink-0 w-12 h-12 rounded-lg me-4 overflow-hidden">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          strokeWidth={2}
          stroke="currentColor"
          className="h-8 w-8 text-gray-500"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"
          />
        </svg>
      </div>
      <div className="w-full">
        <h1 className="mb-4 h-3 w-36 rounded-full bg-gray-300">&nbsp;</h1>
        <div className="mb-2 h-2 w-45 rounded-full bg-gray-300">&nbsp;</div>
      </div>
    </div>
  );
}

export function ImagePlaceholderTagSkeleton() {
  return (
    <div className='nc-Tag flex items-center h-10 min-w-[100px] bg-white hover:bg-neutral-50 text-sm text-neutral-600 dark:text-neutral-300 py-2 px-3 rounded-lg md:py-2.5 md:px-4 dark:bg-neutral-900 mr-2 mb-2'>
      <div className='animate-pulse h-3 w-full rounded-full bg-gray-300'></div>
    </div>
  );
}


export function ImagePlaceholderAuthors() {
  return (
    <div className="flex animate-pulse items-center p-4 gap-5">
      <div className="grid min-h-[40px] min-w-[40px] rounded-full place-items-center bg-gray-300">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          strokeWidth={2}
          stroke="currentColor"
          className="h-6 w-6 text-gray-500"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"
          />
        </svg>
      </div>
      <div className="w-full">
        <h1 className="mb-3 h-3 w-36 rounded-full bg-gray-300">&nbsp;</h1>
        <div className="mb-2 h-2 w-45 rounded-full bg-gray-300">&nbsp;</div>
      </div>
    </div>
  );
}
