import React, { FC } from 'react';
import PostCardSaveAction from '@/components/PostCardSaveAction/PostCardSaveAction';
import PostCardLikeAndComment from '@/components/PostCardLikeAndComment/PostCardLikeAndComment';
import CategoryBadgeList from '@/components/CategoryBadgeList/CategoryBadgeList';
import PostTypeFeaturedIcon from '@/components/PostTypeFeaturedIcon/PostTypeFeaturedIcon';
import Image from 'next/image';
import { Post } from '@/contains/post';
import PostCardMeta from '@/components/PostCardMeta/PostCardMeta';
import { IMG_PLACEHOLDER } from '@/contains/contants';
import { LinkRoute } from '@/components/LinkRoute';
import { readingTime } from 'reading-time-estimator';

// import PostCardMeta from "../PostCardMeta/PostCardMeta";

export interface Card2Props {
  className?: string;
  post: Post;
  size?: 'normal' | 'large';
}

const Card2: FC<Card2Props> = ({
  className = 'h-full',
  size = 'normal',
  post
}) => {
  const {
    name,
    slug,
    image,
    categories,
    content
  } = post;
  const result = readingTime(content ?? '', 100);

  return (
    <div className={`nc-Card2 group relative flex flex-col ${className}`}>
      <div className="block flex-shrink-0 flex-grow relative w-full h-0 pt-[75%] sm:pt-[55%] z-0">
        <Image
          fill
          sizes="(max-width: 600px) 480px, 800px"
          className="object-cover rounded-3xl"
          src={image ?? IMG_PLACEHOLDER}
          alt={name ?? ''}
        />
        <PostTypeFeaturedIcon
          className="absolute bottom-2 left-2"
          postType={'standard'}
          wrapSize="w-8 h-8"
          iconSize="w-4 h-4"
        />
        <CategoryBadgeList
          className="flex flex-wrap absolute top-3 left-3 gap-2"
          itemClass="relative"
          categories={categories}
        />
      </div>

      <LinkRoute href={`/${slug}` ?? '/'} className="absolute inset-0" />

      <div className="mt-5 px-4 flex flex-col">
        <div className="space-y-3">
          <PostCardMeta
            className="relative text-sm"
            avatarSize="h-8 w-8 text-sm"
            meta={post}
          />

          <h2
            className={`nc-card-title block font-semibold text-neutral-900 dark:text-neutral-100 ${
              size === 'large' ? 'text-base sm:text-lg md:text-xl' : 'text-base'
            }`}
          >
            <LinkRoute href={`/${slug}` ?? '/'} className="line-clamp-2" title={name}>
              {name}
            </LinkRoute>
          </h2>
          <p className="text-neutral-500 dark:text-neutral-400 text-[15px] leading-6 line-clamp-2">
            {post.description}
          </p>
        </div>
        <div className="my-5 border-t border-neutral-200 dark:border-neutral-700"></div>
        <div className="flex items-center justify-between">
          <PostCardLikeAndComment className="relative" id={post.id} isCancelGetCountLike={true} liked={post.is_liked} likeCount={post.likes_count} comments_count={post.comments_count} postSlug={post.slug}/>
          <PostCardSaveAction className="relative" readingTime={result.minutes} hidenReadingTime={false} />
        </div>
      </div>
    </div>
  );
};

export default Card2;
