import VerifyIcon from '@/components/VerifyIcon';
// import { GlobeAltIcon } from '@heroicons/react/24/outline';
// import SocialsList from '@/components/SocialsList/SocialsList';
import React from 'react';
import { TAuthor } from '@/contains/author';

interface Props {
  author: TAuthor
}

export const AuthorHeader = ({ ...props }: Props) => {
  const { author } = props;
  const {username, email, first_name, last_name } = author;
  const fullName: string = `${first_name ?? ''} ${last_name ?? ''}`.trim() || 'Author';
  return (
    <div className="pt-5 md:pt-1 lg:ml-6 xl:ml-12 flex-grow">
      <div className="max-w-screen-sm space-y-3.5 ">
        <h2 className="inline-flex items-center text-2xl sm:text-3xl lg:text-4xl font-semibold">
          <span>{fullName}</span>
          <VerifyIcon
            className="ml-2"
            iconClass="w-6 h-6 sm:w-7 sm:h-7 xl:w-8 xl:h-8"
          />
        </h2>
        <span className="block text-sm text-neutral-500 dark:text-neutral-400">{email}</span>
        <span className="block text-sm text-neutral-500 dark:text-neutral-400">@{username}</span>
        {/*<a*/}
        {/*  href="#"*/}
        {/*  className="flex items-center text-xs font-medium space-x-2.5 rtl:space-x-reverse cursor-pointer text-neutral-500 dark:text-neutral-400 truncate"*/}
        {/*>*/}
        {/*  <GlobeAltIcon className="flex-shrink-0 w-4 h-4" />*/}
        {/*  <span className="text-neutral-700 dark:text-neutral-300 truncate">https://example.com/me</span>*/}
        {/*</a>*/}
        {/*<SocialsList itemClass="block w-7 h-7" />*/}
      </div>
    </div>
  );
};
