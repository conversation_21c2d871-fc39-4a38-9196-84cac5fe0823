import React, { FC } from 'react';
import PostCardSaveAction from '@/components/PostCardSaveAction/PostCardSaveAction';
import PostCardLikeAndComment from '@/components/PostCardLikeAndComment/PostCardLikeAndComment';
// import PostCardMeta from "@/components/PostCardMeta/PostCardMeta";
import PostFeaturedMedia from '@/components/PostFeaturedMedia/PostFeaturedMedia';
import { Post } from '@/contains/post';
import CategoryBadgeList from '@/components/CategoryBadgeList/CategoryBadgeList';
import { LinkRoute } from '@/components/LinkRoute';
import PostCardMeta from '@/components/PostCardMeta/PostCardMeta';
import { DateFormat } from '@/components/DateFormat';

export interface Card11Props {
  className?: string;
  post: Post;
  ratio?: string;
  hiddenAuthor?: boolean;
}

const Card11: FC<Card11Props> = ({
  className = 'h-full',
  post,
  hiddenAuthor = false,
  ratio = 'aspect-w-4 aspect-h-3'
}) => {
  const { name, slug, categories, updated_at } = post;

  return (
    <div
      className={`nc-Card11 relative flex flex-col group rounded-3xl overflow-hidden bg-white dark:bg-neutral-900 ${className}`}
      //
    >
      <div
        className={`block flex-shrink-0 relative w-full rounded-t-3xl overflow-hidden z-10 ${ratio}`}
      >
        <div>
          <PostFeaturedMedia post={post} />
        </div>
      </div>
      <LinkRoute href={`/${slug}` ?? '/'} className="absolute inset-0"></LinkRoute>
      <span className="absolute top-3 inset-x-3 z-10">
        <CategoryBadgeList categories={categories} className="gap-2 flex" />
      </span>

      <div className="p-4 flex flex-col space-y-3">
        {!hiddenAuthor ? (
          <PostCardMeta meta={post} />
        ) : (
          <span className="text-xs text-neutral-500"><DateFormat date={updated_at} /></span>
        )}
        <h3 className="nc-card-title block text-base font-semibold text-neutral-900 dark:text-neutral-100">
          <span className="line-clamp-2" title={name}>
            {name}
          </span>
        </h3>
        <p className="text-neutral-500 dark:text-neutral-400 text-[15px] leading-6 line-clamp-1">
          {post.description}
        </p>
        <div className="flex items-end justify-between mt-auto">
          <PostCardLikeAndComment
            className="relative"
            id={post.id}
            liked={post.is_liked}
            likeCount={post.likes_count}
            isCancelGetCountLike={true}
            comments_count={post.comments_count}
            postSlug={slug}
          />
          <PostCardSaveAction className="relative" />
        </div>
      </div>
    </div>
  );
};

export default Card11;
