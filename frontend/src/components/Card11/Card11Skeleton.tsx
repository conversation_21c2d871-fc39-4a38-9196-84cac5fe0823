import React, { FC } from 'react';
import PostCardSaveAction from '@/components/PostCardSaveAction/PostCardSaveAction';
import PostCardLikeAndComment from '@/components/PostCardLikeAndComment/PostCardLikeAndComment';
// import PostCardMeta from "@/components/PostCardMeta/PostCardMeta";
import { IMG_PLACEHOLDER } from '@/contains/contants';
import Image from 'next/image';

export interface Card11Props {
  className?: string;
  ratio?: string;
}

const Card11: FC<Card11Props> = ({
  className = 'h-full',
  ratio = 'aspect-w-4 aspect-h-3'
}) => {
  return (
    <div className={`nc-Card11 relative flex flex-col group rounded-3xl overflow-hidden bg-white dark:bg-neutral-900 ${className}`}>
      <div className={`block flex-shrink-0 relative w-full rounded-t-3xl overflow-hidden z-10 ${ratio}`}>
        <div>
          <div className="nc-PostFeaturedMedia relative w-full h-full">
            <Image
              alt="featured"
              fill
              className="object-cover"
              src={IMG_PLACEHOLDER}
              sizes="(max-width: 600px) 480px, 800px"
            />
          </div>
        </div>
      </div>
      <span className="absolute top-3 inset-x-3 z-10">
        <div className="nc-CategoryBadgeList" data-nc-id="CategoryBadgeList">
          <span className={`nc-Badge  inline-flex px-2.5 py-1 rounded-full font-medium text-xs text-blue-800 bg-blue-100 hover:bg-blue-800`}>Category</span>
        </div>
      </span>
      <div className="p-4 flex flex-col space-y-3">
        <h3 className="nc-card-title animate-pulse block text-base font-semibold text-neutral-900 dark:text-neutral-100">
          <span className="h-3 mb-2 w-full rounded-full bg-gray-300 block">&nbsp;</span>
          <span className="h-3 w-full rounded-full bg-gray-300 block">&nbsp;</span>
        </h3>
        <div className="flex items-end justify-between mt-auto">
          <PostCardLikeAndComment className="relative" id={0} isCancelGetCountLike={true} likeCount={'0'}/>
          <PostCardSaveAction className="relative" />
        </div>
      </div>
    </div>
  );
};

export default Card11;
