import SectionMagazine2 from '@/components/Sections/SectionMagazine2';
import React from 'react';
import { ICategory } from '@/contains/category';
import { categoryApi } from '@/apis/categoryApi';
import { ENDPOINT } from '@/contains/endpoint';

async function getCategoriesMostViewed() {
  const res: ICategory = await categoryApi.get<ICategory>({
    endpoint: ENDPOINT.GET_CATEGORIES_FILTERS_WITH_SCORE,
    params: {
      page: 1,
      per_page: 5,
      order_by: 'total_views'
    }
  }) as ICategory;
  return res.data ?? [];
}

interface ICategoryMostFeaturedWarper {
  heading?: string;
}

export const MostFeaturedCategoriesWarper = async ({ heading }: ICategoryMostFeaturedWarper) => {
  const listCategoriesMostViewed = await getCategoriesMostViewed();
  return (
    <div className="container">
      <SectionMagazine2
        className="py-16 lg:py-24"
        heading={heading || 'Danh sách các danh mục nổi bật'}
        categories={listCategoriesMostViewed}
      />
    </div>
  );
};
