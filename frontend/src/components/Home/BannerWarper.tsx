import SectionLargeSlider from '@/app/(home)/SectionLargeSlider';
import React from 'react';
import { IPost } from '@/contains/post';
import { postServerApi } from '@/apis/postServerApi';
import { ENDPOINT } from '@/contains/endpoint';

async function getPostDataNewest() {
  const res: IPost = await postServerApi.get<IPost>({
    endpoint: ENDPOINT.GET_POSTS_FILTERS_SCORE,
    params: {
      page: 1,
      per_page: 9,
      order_by: 'created_at',
      order: 'desc'
    }
  }) as IPost;
  return res.data ?? [];
}

interface IBannerWarperProps {
  heading?: string;
}

export const BannerWarper = async ({ heading }: IBannerWarperProps) => {
  const listPostNewest = await getPostDataNewest();

  return (
    <div className="container relative">
      <SectionLargeSlider
        heading={heading}
        className="pt-10 pb-16 md:py-16 lg:pb-28 lg:pt-20"
        posts={listPostNewest}
      />
    </div>
  );
};
