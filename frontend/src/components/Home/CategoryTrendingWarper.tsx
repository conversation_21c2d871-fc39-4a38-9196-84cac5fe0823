import SectionSliderNewCategories from '@/components/SectionSliderNewCategories/SectionSliderNewCategories';
import React from 'react';
import { ICategory } from '@/contains/category';
import { categoryApi } from '@/apis/categoryApi';
import { ENDPOINT } from '@/contains/endpoint';

async function getCategoriesDataTrending() {
  const res: ICategory = await categoryApi.get<ICategory>({
    endpoint: ENDPOINT.GET_CATEGORIES_FILTERS_WITH_SCORE,
    params: {
      page: 1,
      per_page: 12,
      order_by: 'total_score',
      order: 'desc'
    }
  }) as ICategory;

  return res.data ?? [];
};

interface ICategoryTrendingWarper {
  heading?: string;
}

export const CategoryTrendingWarper = async ({ heading }: ICategoryTrendingWarper) => {
  const listCategoriesTrending = await getCategoriesDataTrending();
  return (
    <div className="container relative">
      <SectionSliderNewCategories
        className="py-16 lg:py-28"
        heading={heading || 'Danh mục nổi bật'}
        subHeading="Khám phá những bài viết nổi bật nhất trong mọi chủ đề về anime và manga."
        categories={listCategoriesTrending}
        categoryCardType="card4"
      />
    </div>
  );
};
