import React from 'react';
import SectionGridPosts from '@/components/Sections/SectionGridPosts';
import { ENDPOINT } from '@/contains/endpoint';
import { IPost } from '@/contains/post';
import { postServerApi } from '@/apis/postServerApi';

async function getPostDataNewest() {
  const res: IPost = await postServerApi.get<IPost>({
    endpoint: ENDPOINT.GET_POSTS_FILTERS_SCORE,
    params: {
      page: 1,
      per_page: 9,
      order_by: 'created_at',
      order: 'desc'
    }
  }) as IPost;
  return res.data ?? [];
}

interface IGridPostWarperProps {
  heading?: string;
}

export const GridPostWarper = async ({ heading }: IGridPostWarperProps) => {
  const listPostNewest = await getPostDataNewest();
  return (
    <div className="dark bg-neutral-900 dark:bg-black dark:bg-opacity-20 text-neutral-100">
      <div className="relative container">
        <SectionGridPosts
          className="py-16 lg:py-28"
          headingIsCenter
          heading={heading || 'Khám phá các bài viết mới nhất'}
          subHeading="Khám phá các bài viết nổi bật về mọi chủ đề trong anime và manga."
          gridClass="md:grid-cols-2 lg:grid-cols-3"
          posts={listPostNewest}
        />
      </div>
    </div>
  );
};
