import React, { lazy, Suspense } from 'react';
import LoadingEffect from '../LoadingEffect';

const WidgetTags = lazy(() => import('@/components/WidgetTags/WidgetTags'));
const WidgetCategories = lazy(() => import('@/components/WidgetCategories/WidgetCategories'));
const WidgetAuthors = lazy(() => import('@/components/WidgetAuthors/WidgetAuthors'));
const WidgetPosts = lazy(() => import('@/components/WidgetPosts/WidgetPosts'));

const WidgetWarper = () => {
  return (
    <div className="w-full space-y-7 mt-24 lg:mt-0 lg:w-2/5 lg:ps-10 xl:ps-0 xl:w-1/3">
      <Suspense fallback={<LoadingEffect />}>
        <WidgetTags />
      </Suspense>
      <Suspense fallback={<LoadingEffect />}>
        <WidgetCategories />
      </Suspense>
      <Suspense fallback={<LoadingEffect />}>
        <WidgetAuthors />
      </Suspense>
      <Suspense fallback={<LoadingEffect />}>
        <WidgetPosts isHomePage={true} />
      </Suspense>
    </div>
  );
};
export default  WidgetWarper;
