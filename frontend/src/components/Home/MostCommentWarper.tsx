import SectionMagazine1 from '@/components/Sections/SectionMagazine1';
import React from 'react';
import { ICategory } from '@/contains/category';
import { categoryApi } from '@/apis/categoryApi';
import { ENDPOINT } from '@/contains/endpoint';

async function getCategoriesMostCommented() {
  const res: ICategory = await categoryApi.get<ICategory>({
    endpoint: ENDPOINT.GET_CATEGORIES_FILTERS_WITH_SCORE,
    params: {
      page: 1,
      per_page: 5,
      order_by: 'total_comments'
    }
  }) as ICategory;
  return res.data ?? [];
}

interface ICategoryMostCommentedWarper {
  heading?: string;
}

export const MostCommentWarper = async ({ heading }: ICategoryMostCommentedWarper) => {
  const listCategoriesMostCommented = await getCategoriesMostCommented();
  return (
    <div className="container relative">
      <SectionMagazine1
        heading={heading || 'Danh sách các danh mục nhiều bình luận nhất'}
        className="py-16 lg:py-28"
        categories={listCategoriesMostCommented}
      />
    </div>
  );
};
