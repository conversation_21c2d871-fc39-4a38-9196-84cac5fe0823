import React from 'react';
import SectionLatestPosts from '@/components/Sections/SectionLatestPosts';
import WidgetWarper from '@/components/Home/WidgetWarper';

interface INewestPostWarperProps {
  searchParams: {[key: string]: string | undefined};
  className?: string;
  heading?: string;
}

export const NewestPostWarper = ({ searchParams, className, heading }: INewestPostWarperProps) => {
  return (
    <div className="container">
      <div className={`nc-SectionLatestPosts relative ${className}`}>
        <div className="flex flex-col lg:flex-row">
          <SectionLatestPosts searchParams={searchParams} heading={heading}/>
          <WidgetWarper />
        </div>
      </div>
    </div>
  );
};
