import BackgroundSection from '@/components/BackgroundSection/BackgroundSection';
import SectionSliderPosts from '@/components/Sections/SectionSliderPosts';
import React from 'react';
import { IPost } from '@/contains/post';
import { postServerApi } from '@/apis/postServerApi';
import { ENDPOINT } from '@/contains/endpoint';

async function getPostMostScore() {
  const res: IPost = await postServerApi.get<IPost>({
    endpoint: ENDPOINT.GET_POSTS_FILTERS_SCORE,
    params: {
      page: 1,
      per_page: 5
    }
  }) as IPost;
  return res.data ?? [];
}

interface IPostMostScoreWarper {
  heading?: string;
}

export const MostScoreWarper = async ({ heading }: IPostMostScoreWarper) => {
  const listPostMostScore = await getPostMostScore();
  return (
    <div className="container">
      <div className="relative py-16">
        <BackgroundSection />
        <SectionSliderPosts
          heading={heading || 'Danh sách bài viết nhiều điểm nhất'}
          subHeading={`Hơn ${listPostMostScore.length} bài viết có nhiều điểm nhất`}
          posts={listPostMostScore}
        />
      </div>
    </div>
  );
};
