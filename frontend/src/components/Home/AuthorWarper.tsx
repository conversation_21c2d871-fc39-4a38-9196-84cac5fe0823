import BackgroundSection from '@/components/BackgroundSection/BackgroundSection';
import SectionSliderNewAuthors from '@/components/SectionSliderNewAthors/SectionSliderNewAuthors';
import React from 'react';
import { IAuthor } from '@/contains/author';
import { authorApi } from '@/apis/authorApi';

async function getListAuthors() {
  const res: IAuthor = await authorApi.get<IAuthor>({
    params: {
      page: 1,
      per_page: 5,
      order_by: 'created_at',
      order: 'desc'
    }
  }) as IAuthor;
  return res?.data ?? [];
}

interface IAuthorWarperProps {
  heading?: string;
}

export const AuthorWarper = async ({ heading }: IAuthorWarperProps) => {
  const listAuthors = await getListAuthors();

  return (
    <div className="container relative">
      <div className="relative py-16">
        <BackgroundSection />
        <SectionSliderNewAuthors
          heading={heading || 'Những tác giả mới nhất'}
          subHeading="Chào đón tiềm năng sáng tạo của tương lai."
          authors={listAuthors}
        />
      </div>
    </div>
  );
};
