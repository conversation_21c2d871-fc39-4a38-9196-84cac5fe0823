import { Route } from '@/routers/types';
import React, { FC, ReactNode } from 'react';
import { LinkRoute } from '@/components/LinkRoute';
import convertNumbThousand from '@/utils/convertNumbThousand';
import { getColorClassWithHover, getColorCustom } from '@/utils/generateColorCSS';

export interface BadgeProps {
  className?: string;
  name: ReactNode;
  color?: string;
  href?: Route;
}

export interface BadgeCounterProps {
  className?: string;
  name: ReactNode;
  color?: string;
  count?: number;
}

const Badge: FC<BadgeProps> = ({
  className = 'relative',
  name,
  color = '',
  href
}) => {

  const CLASSES =
    'nc-Badge  inline-flex px-2.5 py-1 rounded-full font-medium text-xs ' +
    className;
  const { adjustedColor, baseColor } = getColorCustom(color);
  return !!href ? (
    <LinkRoute
      href={href ? `/category/${href}` as Route : '/'}
      className={`transition-colors hover:text-white duration-300 ${CLASSES}`}
      style={{
        color: baseColor,
        backgroundColor: adjustedColor
      }}
    >
      {name}
    </LinkRoute>
  ) : (
    <span
      className={`${CLASSES} `}
      style={{
        color: baseColor,
        backgroundColor: adjustedColor
      }}
    >{name}</span>
  );
};

export default Badge;

export const BadgeCounter: FC<BadgeCounterProps> = ({
  className = 'relative',
  color = '',
  count = 0
}) => {
  const CLASSES =
    'nc-Badge  inline-flex px-2.5 py-1 rounded-full font-medium text-xs ' +
    className;
  return <span className={`${CLASSES} ${getColorClassWithHover(false, color)} `}>+{convertNumbThousand(count)}</span>;
};
