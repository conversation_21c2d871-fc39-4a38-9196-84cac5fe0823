import React, { FC } from 'react';
import { TaxonomyType, TwMainColor } from '@/data/types';
import Badge from '@/components/Badge/Badge';
import Image from 'next/image';
import { LinkRoute } from '@/components/LinkRoute';
import convertNumbThousand from '@/utils/convertNumbThousand';
import { TCategory } from '@/contains/category';
import { IMG_PLACEHOLDER } from '@/contains/contants';
import { getColorClass } from '@/utils/generateColorCSS';

export interface CardCategory4Props {
  className?: string;
  category: TCategory;
  index?: string;
}

const CardCategory4: FC<CardCategory4Props> = ({
  className = "",
  category,
  index,
}) => {
  const { posts_count, name, slug = '/', color, image  } = category;
  return (
    <LinkRoute href={`/category/${slug}`} className={`nc-CardCategory4 flex flex-col ${className}`}>
      <div className="flex-shrink-0 relative w-full aspect-w-7 aspect-h-5 h-0 rounded-3xl overflow-hidden group">
        <Image
          alt="Card Category"
          fill
          src={image || IMG_PLACEHOLDER}
          className="object-cover w-full h-full rounded-2xl"
          sizes="(min-width: 1024px) 20rem, (min-width: 640px) 16rem, 12rem"
        />
        <div>
          {index && (
            <Badge
              name={index}
              className="absolute top-3 start-3"
              color={color || ''}
            />
          )}
        </div>
        <span className="opacity-0 group-hover:opacity-100 absolute inset-0 bg-black bg-opacity-10 transition-opacity"></span>
      </div>

      <div className="flex items-center mt-5">
        <div className={`w-9 h-9 ${getColorClass('')} rounded-full`} style={{
          backgroundColor: color,
        }}></div>
        <div className="ms-4">
          <h2 className="text-base text-neutral-900 dark:text-neutral-100 font-medium">
            {name}
          </h2>
          <span className="block text-sm text-neutral-500 dark:text-neutral-400">
            {convertNumbThousand(posts_count)} Bài viết
          </span>
        </div>
      </div>
    </LinkRoute>
  );
};

export default CardCategory4;
