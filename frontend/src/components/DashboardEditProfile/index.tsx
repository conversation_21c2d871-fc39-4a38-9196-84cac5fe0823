'use client';

import React, { useEffect, useState } from 'react';
import { SubmitHand<PERSON>, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { toast } from 'react-toastify';
import ButtonPrimary from '@/components/Button/ButtonPrimary';
import Input from '@/components/Input/Input';
import Label from '@/components/Label/Label';
import { authApi } from '@/apis/authApi';
import {
  schemaUpdateProfile,
  schemaUploadAvatar,
  UpdateProfileFormValues,
  UploadAvatarFormValues
} from '@/data/validation';
import Image from 'next/image';
import Loading from '@/components/Button/Loading';
import { useUserServer } from '@/hooks/useUser';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
dayjs.extend(utc);

const DashboardEditProfile = () => {
  const { data: user } = useUserServer();
  const [loading, setLoading] = useState(false);

  const [avatarPreview, setAvatarPreview] = useState<string | null>(user?.data?.avatar ?? null);
  const [loadingUpload, setLoadingUpload] = useState(false);

  useEffect(() => {
    if (user?.data?.avatar) {
      setAvatarPreview(user?.data?.avatar);
    }
  }, [user?.data?.avatar]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<UpdateProfileFormValues>({
    resolver: yupResolver(schemaUpdateProfile),
    defaultValues: {
      email: user?.data?.email || '--',
      name: user?.data?.first_name || '--',
      phone: user?.data?.phone || '--',
      dob: user?.data?.dob || '--',
      gender: user?.data?.gender || '',
      description: user?.data?.description || '--'
    }
  });

  const {
    handleSubmit: handleSubmitAvatar,
    reset: resetAvatar,
    setValue: setValueAvatar
  } = useForm<UploadAvatarFormValues>({
    resolver: yupResolver(schemaUploadAvatar),
    defaultValues: {
      avatar: user?.data?.avatar ?? null
    }
  });

  useEffect(() => {
    if (!!user?.data) {
      const dob = user.data.dob
        ? dayjs.utc(user.data.dob).format('YYYY-MM-DD')
        : '';
      reset({
        email: user?.data?.email || '',
        name: user?.data?.name || '',
        phone: user?.data?.phone || '',
        dob: dob,
        gender: user?.data?.gender || '',
        description: user?.data?.description || ''
      });
    }
  }, [user, reset]);

  const onSubmit: SubmitHandler<UpdateProfileFormValues> = async (data) => {
    setLoading(true);
    try {
      const res = await authApi.put({
        endpoint: 'me',
        payload: JSON.stringify(
          Object.fromEntries(Object.entries(data).filter(([_, value]) => value !== '' && value !== undefined))
        )
      });

      if (!res.error) {
        toast.success('Cập nhật thông tin thành công');
      } else {
        toast.error(res.error?.error?.message || 'Cập nhật thông tin không thành công');
      }
    } catch {
      toast.error('Cập nhật thông tin không thành công');
    } finally {
      setLoading(false);
    }
  };

  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    if (!file) {
      return;
    }

    if (!file.type.startsWith('image/')) {
      toast.error('Chỉ cho phép upload file hình ảnh!');
      return;
    }

    if (file.size > 2 * 1024 * 1024) {
      toast.error(`File ảnh quá lớn (${( file.size / ( 1024 * 1024 ) ).toFixed(2)}MB), phải nhỏ hơn 2MB!`);
      return;
    }

    setValueAvatar('avatar', file);
    setAvatarPreview(URL.createObjectURL(file));
  };

  const onSubmitAvatar: SubmitHandler<UploadAvatarFormValues> = async (data) => {
    if (!data.avatar) {
      console.warn('No avatar file selected, skip upload.');
      return;
    }

    setLoadingUpload(true);

    const formData = new FormData();
    formData.append('avatar', data.avatar);

    try {
      const response = await authApi.postFile({
        endpoint: 'update/avatar',
        payload: formData
      });

      if (response.data && response.data.avatar) {
        setAvatarPreview(response.data.avatar);
        toast.success('Upload ảnh thành công!');
      } else {
        toast.error('Upload ảnh thất bại!');
      }
    } catch (error) {
      toast.error('Có lỗi khi upload ảnh!');
    } finally {
      setLoadingUpload(false);
      resetAvatar({ avatar: null });
    }
  };

  return (
    <div className="rounded-xl md:border md:border-neutral-100 dark:border-neutral-800 md:p-6">
      <form className="grid md:grid-cols-2 gap-6 mb-10">

        <label className="block md:col-span-2">
          <Label>Ảnh đại diện</Label>
          {avatarPreview && (
            <div className="mt-3 flex justify-center mb-3">
              <Image
                src={avatarPreview}
                alt="Avatar Preview"
                width={100}
                height={100}
                className="rounded-full object-cover"
              />
            </div>
          )}
          <input
            type="file"
            accept="image/*"
            onChange={handleAvatarChange}
            className="mt-1 block w-full text-neutral-700 dark:text-neutral-200"
            disabled={loadingUpload}
          />
        </label>

        <ButtonPrimary
          className="md:col-span-2 md:max-h-10 md:max-w-fit md:ml-auto" type="button"
          disabled={loadingUpload}
          onClick={() => handleSubmitAvatar(onSubmitAvatar)()}
        >
          Cập nhật ảnh đại diện
          {loadingUpload && <span className="ml-4"><Loading /></span>}
        </ButtonPrimary>

      </form>

      {/*update profile*/}

      <form className="grid md:grid-cols-2 gap-6" onSubmit={handleSubmit(onSubmit)}>
        <label className="block">
          <Label>Email</Label>
          <Input
            type="email"
            {...register('email')}
            placeholder="Nhập email"
            className="mt-1"
          />
          {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>}
        </label>

        <label className="block">
          <Label>Số điện thoại</Label>
          <Input
            type="text"
            {...register('phone')}
            placeholder="Nhập số điện thoại"
            className="mt-1"
          />
          {errors.phone && <p className="text-red-500 text-sm mt-1">{errors.phone.message}</p>}
        </label>

        <label className="block">
          <Label>Tên hiển thị</Label>
          <Input
            type="text"
            {...register('name')}
            placeholder="Nhập tên hiển thị"
            className="mt-1"
          />
          {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>}
        </label>

        <label className="block">
          <Label>Giới tính</Label>
          <select
            {...register('gender')}
            className="mt-1 block w-full rounded-full border-neutral-300 dark:border-neutral-700 dark:bg-neutral-900 dark:text-neutral-200"
          >
            <option value="">Chọn giới tính</option>
            <option value="male">Nam</option>
            <option value="female">Nữ</option>
            <option value="other">Khác</option>
          </select>
          {errors.gender && <p className="text-red-500 text-sm mt-1">{errors.gender.message}</p>}
        </label>

        <label className="block">
          <Label>Ngày sinh</Label>
          <Input
            type="date"
            {...register('dob')}
            className="mt-1"
          />
          {errors.dob && <p className="text-red-500 text-sm mt-1">{errors.dob.message}</p>}
        </label>

        <label className="block md:col-span-2">
          <Label>Giới thiệu bản thân</Label>
          <textarea
            {...register('description')}
            rows={4}
            className="mt-1 block w-full rounded-xl border-neutral-300 dark:border-neutral-700 dark:bg-neutral-900 dark:text-neutral-200"
            placeholder="Mô tả ngắn về bạn..."
          />
          {errors.description && <p className="text-red-500 text-sm mt-1">{errors.description.message}</p>}
        </label>

        <ButtonPrimary
          className="md:col-span-2" type="button"
          disabled={loading}
          onClick={() => handleSubmit(onSubmit)()}
        >
          Cập nhật thông tin{loading && <span className="ml-4"><Loading /></span>}
        </ButtonPrimary>
      </form>
    </div>
  );
};

export default DashboardEditProfile;
