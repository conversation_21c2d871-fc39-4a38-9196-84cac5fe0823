import React, { FC } from 'react';
import { TCategory } from '@/contains/category';
import NcImage from '@/components/NcImage/NcImage';
import { IMG_PLACEHOLDER } from '@/contains/contants';
import { LinkRoute } from '@/components/LinkRoute';

export interface CardCategory1Props {
  className?: string;
  categories: TCategory;
  size?: 'large' | 'normal';
  hideImage?: boolean;
}

const CardCategory1: FC<CardCategory1Props> = ({
  className = '',
  size = 'normal',
  categories,
  hideImage
}) => {
  const name = categories?.name || '';
  const slug = categories?.slug || '/';
  const post_count = categories?.posts_count ?? 0;
  const image = categories?.image || IMG_PLACEHOLDER;
  return (
    <LinkRoute
      href={`/category/${slug}`}
      className={`nc-CardCategory1 flex items-center ${className}`}
    >

      <NcImage
        alt=""
        containerClassName={`relative flex-shrink-0 ${
          size === 'large' ? 'w-20 h-20' : 'w-12 h-12'
        } rounded-lg me-4 overflow-hidden`}
        src={image}
        fill
        className="object-cover"
        sizes="80px"
      />

      <div>
        <h2
          className={`${
            size === 'large' ? 'text-lg' : 'text-base'
          } nc-card-title text-neutral-900 dark:text-neutral-100 text-sm sm:text-base font-medium sm:font-semibold`}
        >
          {name}
        </h2>
        <span
          className={`${
            size === 'large' ? 'text-sm' : 'text-xs'
          } block mt-[2px] text-neutral-500 dark:text-neutral-400`}
        >
          {post_count} Articles
        </span>
      </div>
    </LinkRoute>
  );
};

export default CardCategory1;
