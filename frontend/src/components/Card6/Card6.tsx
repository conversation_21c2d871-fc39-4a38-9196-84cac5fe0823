import React, { FC } from 'react';
import PostCardLikeAndComment from '@/components/PostCardLikeAndComment/PostCardLikeAndComment';
import CategoryBadgeList from '@/components/CategoryBadgeList/CategoryBadgeList';
import PostTypeFeaturedIcon from '@/components/PostTypeFeaturedIcon/PostTypeFeaturedIcon';
import Image from 'next/image';
import { Post } from '@/contains/post';
import { IMG_PLACEHOLDER } from '@/contains/contants';
import { LinkRoute } from '@/components/LinkRoute';
import PostCardMeta from '@/components/PostCardMeta/PostCardMeta';

export interface Card6Props {
  className?: string;
  post: Post;
}

const Card6: FC<Card6Props> = ({ className = 'h-full', post }) => {
  const { slug, categories, image, name } = post;

  return (
    <div
      className={`nc-Card6 relative flex group flex-row items-center sm:p-4 sm:rounded-3xl sm:bg-white sm:dark:bg-neutral-900 sm:border border-neutral-200 dark:border-neutral-700 ${className}`}
    >
      <LinkRoute href={`/${slug}` ?? '/'} className="absolute inset-0 z-0"></LinkRoute>
      <div className="flex flex-col flex-grow">
        <div className="space-y-3 mb-4">
          <CategoryBadgeList categories={categories} />
          <h2 className={`block font-semibold text-sm sm:text-base`}>
            <LinkRoute href={`/${slug}` ?? '/'} className="line-clamp-2" title={name ?? ''}>
              {name ?? ''}
            </LinkRoute>
          </h2>
          <PostCardMeta meta={{ ...post }} />
        </div>
        <div className="flex items-center flex-wrap justify-between mt-auto">
          <PostCardLikeAndComment className="relative" id={post.id} isCancelGetCountLike={true} liked={post.is_liked} likeCount={post.likes_count} comments_count={post.comments_count} postSlug={slug}/>
          {/*<PostCardSaveAction className="relative" readingTime={readingTime} />*/}
        </div>
      </div>

      <LinkRoute
        href={`/${slug}` ?? '/'}
        className={`block relative flex-shrink-0 w-24 h-24 sm:w-40 sm:h-full ms-3 sm:ms-5 rounded-2xl overflow-hidden z-0`}
      >
        <Image
          sizes="(max-width: 600px) 180px, 400px"
          className="object-cover w-full h-full"
          fill
          src={image ?? IMG_PLACEHOLDER}
          alt={name}
        />
        <span className="absolute bottom-1 start-1">
          <PostTypeFeaturedIcon
            wrapSize="h-7 w-7"
            iconSize="h-4 w-4"
            postType={'standard'}
          />
        </span>
      </LinkRoute>
    </div>
  );
};

export default Card6;
