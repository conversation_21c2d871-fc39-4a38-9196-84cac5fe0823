import Card3Small from '@/components/Card3Small/Card3Small';
import WidgetHeading1 from '@/components/WidgetHeading1/WidgetHeading1';
import { TCategory } from '@/contains/category';
import { Post } from '@/contains/post';
import Nodata from '@/components/Nodata';
import { postApi } from '@/apis/postApi';

export interface WidgetPostsProps {
  className?: string;
  isHomePage?: boolean;
  categories?: TCategory[];
}

async function fetchPosts(categories?: TCategory[]) {
  try {
    const { data, error } = await postApi.get<Post[]>({
      params: {
        page: 1,
        per_page: 4,
        ...( categories?.length ? { categories: categories.map((c) => c.id).join(',') } : {} )
      }
    });

    return !error ? data ?? [] : [];
  } catch (err) {
    console.error('Failed to fetch posts', err);
    return [];
  }
}

const WidgetPosts = async ({
  className = 'bg-neutral-100 dark:bg-neutral-800',
  isHomePage = false,
  categories
}: WidgetPostsProps) => {
  const posts = await fetchPosts(categories);

  return (
    <div className={`nc-WidgetPosts rounded-3xl overflow-hidden lg:sticky lg:top-[100px] ${className}`}>
      <WidgetHeading1
        title="🎯 Bài viết phổ biến"
        viewAll={{ label: 'Xem tất cả', href: '/search' }}
      />
      <div className="flex flex-col divide-y divide-neutral-200 dark:divide-neutral-700">
        {posts.length > 0 ? (
          posts.map((post) => (
            <Card3Small
              className="p-4 xl:px-5 xl:py-6 hover:bg-neutral-200 dark:hover:bg-neutral-700"
              key={post.id}
              post={post}
            />
          ))
        ) : (
          <Nodata />
        )}
      </div>
    </div>
  );
};

export default WidgetPosts;
