'use client';

import React from 'react';
import { useSearchParams } from 'next/navigation';
import twFocusClass from '@/utils/twFocusClass';
import { LinkRoute } from '@/components/LinkRoute';

export interface PaginationProps {
  className?: string;
  total?: number;
  perPage?: number;
  currentPage?: number;
  onPageChange?: (page: number) => void;
}

const Pagination: React.FC<PaginationProps> = ({
  className = '',
  total = 0,
  perPage = 10,
  currentPage = 1,
  onPageChange,
}) => {
  const totalPages = Math.ceil(total / perPage);
  const searchParams = useSearchParams();

  const createLink = (page: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('page', String(page));
    params.set('per_page', String(perPage));
    return `?${params.toString()}`;
  };

  const handleClick = (page: number) => {
    if (onPageChange) onPageChange(page);
  };

  const renderItem = (page: number, index: number) => {
    const isActive = page === currentPage;
    const href = createLink(page);

    if (isActive) {
      return (
        <span
          key={index}
          className={`inline-flex w-11 h-11 items-center justify-center rounded-full bg-primary-6000 text-white ${twFocusClass()}`}
        >
          {page}
        </span>
      );
    }

    return (
      <LinkRoute
        key={index}
        href={href}
        prefetch={true}
        scroll={false}
        onClick={() => handleClick(page)}
        className={`inline-flex w-11 h-11 items-center justify-center rounded-full bg-white hover:bg-neutral-100 
        border border-neutral-200 text-neutral-6000 dark:text-neutral-400 
        dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:border-neutral-700 ${twFocusClass()}`}
      >
        {page}
      </LinkRoute>
    );
  };

  const pages = Array.from({ length: totalPages }, (_, index) => index + 1);

  if (totalPages <= 1) return null;

  return (
    <nav
      className={`nc-Pagination inline-flex space-x-1 rtl:space-x-reverse text-base font-medium ${className}`}
    >
      {currentPage > 1 && (
        <LinkRoute
          href={createLink(currentPage - 1)}
          scroll={false}
          onClick={() => handleClick(currentPage - 1)}
          prefetch={true}
          className={`inline-flex w-11 h-11 items-center justify-center rounded-full bg-white hover:bg-neutral-100 
          border border-neutral-200 text-neutral-6000 dark:text-neutral-400 
          dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:border-neutral-700 ${twFocusClass()}`}
        >
          &laquo;
        </LinkRoute>
      )}

      {pages.map((page, index) => renderItem(page, index))}

      {currentPage < totalPages && (
        <LinkRoute
          href={createLink(currentPage + 1)}
          scroll={false}
          onClick={() => handleClick(currentPage + 1)}
          prefetch={true}
          className={`inline-flex w-11 h-11 items-center justify-center rounded-full bg-white hover:bg-neutral-100 
          border border-neutral-200 text-neutral-6000 dark:text-neutral-400 
          dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:border-neutral-700 ${twFocusClass()}`}
        >
          &raquo;
        </LinkRoute>
      )}
    </nav>
  );
};

export default Pagination;
