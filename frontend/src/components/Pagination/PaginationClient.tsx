'use client';

import React from 'react';
import twFocusClass from '@/utils/twFocusClass';

export interface PaginationProps {
  className?: string;
  total?: number;
  perPage?: number;
  currentPage?: number;
  onPageChange?: (page: number) => void;
}

const PaginationClient: React.FC<PaginationProps> = ({
  className = '',
  total = 0,
  perPage = 10,
  currentPage = 1,
  onPageChange,
}) => {
  const totalPages = Math.ceil(total / perPage);

  const handleClick = (page: number) => {
    if (onPageChange) onPageChange(page);
  };

  const renderItem = (page: number, index: number) => {
    const isActive = page === currentPage;

    if (isActive) {
      return (
        <button
          key={index}
          disabled
          className={`inline-flex w-11 h-11 items-center justify-center rounded-full bg-primary-6000 text-white ${twFocusClass()}`}
        >
          {page}
        </button>
      );
    }

    return (
      <button
        key={index}
        onClick={() => handleClick(page)}
        className={`inline-flex w-11 h-11 items-center justify-center rounded-full bg-white hover:bg-neutral-100 
        border border-neutral-200 text-neutral-6000 dark:text-neutral-400 
        dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:border-neutral-700 ${twFocusClass()}`}
      >
        {page}
      </button>
    );
  };

  const pages = Array.from({ length: totalPages }, (_, index) => index + 1);

  if (totalPages <= 1) return null;

  return (
    <nav
      className={`nc-Pagination inline-flex space-x-1 rtl:space-x-reverse text-base font-medium ${className}`}
    >
      {currentPage > 1 && (
        <button
          onClick={() => handleClick(currentPage - 1)}
          className={`inline-flex w-11 h-11 items-center justify-center rounded-full bg-white hover:bg-neutral-100 
          border border-neutral-200 text-neutral-6000 dark:text-neutral-400 
          dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:border-neutral-700 ${twFocusClass()}`}
        >
          &laquo;
        </button>
      )}

      {pages.map((page, index) => renderItem(page, index))}

      {currentPage < totalPages && (
        <button
          onClick={() => handleClick(currentPage + 1)}
          className={`inline-flex w-11 h-11 items-center justify-center rounded-full bg-white hover:bg-neutral-100 
          border border-neutral-200 text-neutral-6000 dark:text-neutral-400 
          dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:border-neutral-700 ${twFocusClass()}`}
        >
          &raquo;
        </button>
      )}
    </nav>
  );
};

export default PaginationClient;
