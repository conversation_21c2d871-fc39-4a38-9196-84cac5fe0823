import React from 'react';
import { LinkRoute } from '@/components/LinkRoute';
import Image from 'next/image';

export interface LogoProps {
  img: string;
  height?: number;
}

const Logo: React.FC<LogoProps> = ({
  img,
  height
}) => {
  return (
    <LinkRoute
      href="/"
      className="ttnc-logo inline-block text-primary-6000 flex-shrink-0"
    >
      <Image src={img} alt={'logo'} width={height} height={height} quality={100}/>
    </LinkRoute>
  );
};

export default Logo;
