import { ApiResponseMeta, IPost, Post } from '@/contains/post';
import { postServerApi } from '@/apis/postServerApi';
import { ENDPOINT } from '@/contains/endpoint';
import { ITEM_PER_PAGE } from '@/utils/constant';
import Heading from '@/components/Heading/Heading';
import Card3 from '@/components/Card3/Card3';
import Pagination from '@/components/Pagination/Pagination';
import React from 'react';
import { SectionLatestPostsProps } from '@/components/Sections/SectionLatestPosts';

async function getPostData(payload: {page: number}) {
  const { page } = payload;
  const res: IPost = await postServerApi.get<IPost>({
    endpoint: ENDPOINT.GET_POSTS_FILTERS_SCORE,
    params: {
      page,
      per_page: ITEM_PER_PAGE
    },
  }) as IPost;
  const data: Post[] = res?.data ?? [];
  const meta: ApiResponseMeta = res?.meta ?? {
    current_page: 1,
    total: 0
  } as ApiResponseMeta;
  return {
    posts: data ?? [],
    meta: meta
  };
}

export const SectionPostWarper = async ({
  heading = '<PERSON>ác bài viết mới nhất🎈',
  gridClass = '',
  searchParams
}: SectionLatestPostsProps) => {
  const page: number = parseInt(( searchParams?.page as string ) || '1');
  const { posts, meta } = await getPostData({ page });
  const { total, current_page } = meta;
  return (
    <div className="w-full lg:w-3/5 xl:w-2/3 xl:pe-14">
      <Heading desc={'Khám phá những bài viết nổi bật nhất về anime và manga.'}>{heading}</Heading>
      <div className={`grid gap-6 md:gap-8 ${gridClass}`}>
        {posts.map((post, index) => <Card3 key={index} className="py-3" post={post} />)}
      </div>
      <div className="flex flex-col mt-12 md:mt-20 space-y-5 sm:space-y-0 sm:space-x-3 rtl:space-x-reverse sm:flex-row sm:justify-between sm:items-center">
        <Pagination perPage={ITEM_PER_PAGE} total={total} currentPage={current_page} />
      </div>
    </div>
  );
};
