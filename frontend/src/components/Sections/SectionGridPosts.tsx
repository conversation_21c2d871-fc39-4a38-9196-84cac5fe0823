import React, { FC, ReactNode } from 'react';
import Heading from '@/components/Heading/Heading';
import ButtonPrimary from '@/components/Button/ButtonPrimary';
import { Post } from '@/contains/post';
import Card10V2 from '@/components/Card10/Card10V2';

export interface SectionGridPostsProps {
  posts?: Post[];
  className?: string;
  gridClass?: string;
  heading?: ReactNode;
  subHeading?: ReactNode;
  headingIsCenter?: boolean;
}

const SectionGridPosts: FC<SectionGridPostsProps> = ({
  className = '',
  gridClass = '',
  heading,
  subHeading,
  headingIsCenter,
  posts
}) => {
  return (
    <div className={`nc-SectionGridPosts relative ${className}`}>
      <Heading desc={subHeading} isCenter={headingIsCenter}>
        {heading}
      </Heading>
      <div className={`grid gap-6 md:gap-8 ${gridClass}`}>
        {posts?.map((post) =>
          <Card10V2 key={post.id} post={post} />
        )}
      </div>
      <div className="flex mt-20 justify-center items-center">
        <ButtonPrimary href={'/search'}>Xem tất cả</ButtonPrimary>
      </div>
    </div>
  );
};

export default SectionGridPosts;
