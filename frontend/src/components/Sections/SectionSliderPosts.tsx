'use client';

import React, { FC } from 'react';
import Heading from '@/components/Heading/Heading';
import Card11 from '@/components/Card11/Card11';
import MySlider from '@/components/MySlider';
import { Post } from '@/contains/post';

export interface SectionSliderPostsProps {
  className?: string;
  heading: string;
  subHeading?: string;
  posts: Post[];
  perView?: 2 | 3 | 4;
}

const SectionSliderPosts: FC<SectionSliderPostsProps> = ({
  heading,
  subHeading,
  className = '',
  posts,
  perView = 4
}) => {
  return (
    <div className={`nc-SectionSliderPosts ${className}`}>
      <Heading desc={subHeading} isCenter>
        {heading}
      </Heading>

      <MySlider
        data={posts}
        renderItem={(item, indx) => <Card11 key={indx} post={item} />}
        itemPerRow={perView}
      />
    </div>
  );
};

export default SectionSliderPosts;
