import React, { FC, Suspense } from 'react';
import { SectionPostWarper } from '@/components/Sections/SectionPostWarper';
import LoadingEffect from '../LoadingEffect';

export interface SectionLatestPostsProps {
  gridClass?: string;
  className?: string;
  heading?: string;
  searchParams?: {[key: string]: string | undefined};
}

const SectionLatestPosts: FC<SectionLatestPostsProps> = ({
  heading = 'Các bài viết mới nhất🎈',
  gridClass = '',
  className = '',
  searchParams
}) => {
  return (
    <Suspense fallback={<LoadingEffect />}>
      <SectionPostWarper
        className={className}
        gridClass={gridClass}
        searchParams={searchParams}
        heading={heading}
      />
    </Suspense>
  );
};
export default SectionLatestPosts;
