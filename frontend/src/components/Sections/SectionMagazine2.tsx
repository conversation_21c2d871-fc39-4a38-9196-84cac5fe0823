'use client';

import React, { FC, useEffect, useState } from 'react';
import Card2 from '@/components/Card2/Card2';
import Card11 from '@/components/Card11/Card11';
import { IPost, Post } from '@/contains/post';
import { postApi } from '@/apis/postApi';
import { ENDPOINT } from '@/contains/endpoint';
import { TCategory } from '@/contains/category';
import HeaderFilterHome from '@/components/Sections/HeaderFilterHome';
import LoadingEffect from '../LoadingEffect';
import { getCookie } from 'cookies-next/client';
import Nodata from '@/components/Nodata';

export interface SectionMagazine2Props {
  heading?: string;
  categories?: TCategory[];
  className?: string;
}

const SectionMagazine2: FC<SectionMagazine2Props> = ({
  heading = '🎈 Latest Articles',
  categories = [],
  className
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [posts, setPosts] = useState<Post[]>([]);

  const handleGetPostsByCate = async (cate: string) => {
    setLoading(true);
    const categoryId = categories.find((item) => item.name.toLowerCase() === cate.toLowerCase())?.id;
    const token = getCookie('token');
    await postApi.get<IPost>({
      endpoint: ENDPOINT.GET_POSTS_FILTERS_SCORE,
      params: {
        ...( categoryId && { 'categories[]': categoryId } ),
        page: 1,
        per_page: 5
      },
      headers: token ? { Authorization: `Bearer ${token}` } : undefined
    }).then((res) => {
      setPosts(res.data as Post[]);
    }).finally(() => setLoading(false));
  };

  const handleChangeCate = (cate: string) => {
    handleGetPostsByCate(cate);
  };

  useEffect(() => {
    handleGetPostsByCate('');
  }, []);

  const handleRenderUI = () => {
    if (loading) {
      return <LoadingEffect />;
    }

    if (!posts?.length) {
      return <Nodata />;
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <div className="grid gap-6">
          {!loading && posts && posts.length > 0 && posts.filter((_, i) => i < 3 && i > 0).map((item, index) => {
            return (
              <Card11 ratio="aspect-w-5 aspect-h-3" key={index} post={item} />
            );
          })}
        </div>
        <div className="lg:col-span-2">
          {loading && <LoadingEffect />}
          {!loading && posts && posts.length > 0 && posts[0] && <Card2 size="large" post={posts[0]} />}
        </div>
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 xl:grid-cols-1 md:col-span-3 xl:col-span-1">
          {!loading && posts && posts.length > 0 && posts.filter((_, i) => i < 5 && i >= 3).map((item, index) => {
            return (
              <Card11 ratio="aspect-w-5 aspect-h-3" key={index} post={item} />
            );
          })}
        </div>
      </div>
    );
  };
  return (
    <div className={`nc-SectionMagazine2 ${className}`}>
      <HeaderFilterHome
        heading={heading}
        tabs={['All Item'].concat(categories.map((item) => item.name) ?? [''])}
        handleChangeCate={handleChangeCate}
      />
      {handleRenderUI()}
    </div>
  );
};

export default SectionMagazine2;
