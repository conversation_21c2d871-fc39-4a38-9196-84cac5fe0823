'use client';
import React, { FC, useEffect, useState } from 'react';
import Card2 from '@/components/Card2/Card2';
import Card6 from '@/components/Card6/Card6';
import { IPost, Post } from '@/contains/post';
import { TCategory } from '@/contains/category';
import HeaderFilterHome from '@/components/Sections/HeaderFilterHome';
import { postApi } from '@/apis/postApi';
import { ENDPOINT } from '@/contains/endpoint';
import LoadingEffect from '../LoadingEffect';
import { getCookie } from 'cookies-next/client';
import Nodata from '@/components/Nodata';

export interface SectionMagazine1Props {
  heading?: string;
  categories?: TCategory[];
  className?: string;
}

const SectionMagazine1: FC<SectionMagazine1Props> = ({
  heading = 'Các bài viết mới nhất🎈 ',
  categories = [],
  className = ''
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [posts, setPosts] = useState<Post[]>([]);

  const handleGetPostsByCate = async (cate: string) => {
    setLoading(true);
    const categoryId = categories.find((item: TCategory) => item.name.toLowerCase() === cate.toLowerCase())?.id;
    const token = getCookie('token');

    await postApi.get<IPost>({
      endpoint: ENDPOINT.GET_POSTS_FILTERS_SCORE,
      params: {
        ...( categoryId && { 'categories[]': categoryId } ),
        page: 1,
        per_page: 4
      },
      headers: token ? { Authorization: `Bearer ${token}` } : undefined
    }).then((res) => {
      setPosts(res.data as Post[]);
    }).finally(() => setLoading(false));
  };

  const handleChangeCate = (cate: string) => {
    handleGetPostsByCate(cate);
  };

  useEffect(() => {
    handleGetPostsByCate('');
  }, []);

  const handleRenderUI = () => {
    if (loading) {
      return <LoadingEffect />;
    }

    if (!posts?.length) {
      return <Nodata />;
    }

    return (
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 md:gap-8">
        {!loading && posts?.length > 0 && posts[0] && <Card2 size="large" post={posts[0]} />}
        {!loading && <div className="grid gap-6 md:gap-8">
          {posts?.filter((_, i) => i < 4 && i > 0).map((item, index) => (
            <Card6 key={index} post={item} />
          ))}
        </div>}
      </div>
    );
  };

  return (
    <div className={`nc-SectionMagazine1 ${className}`}>
      <HeaderFilterHome
        heading={heading}
        tabs={['All Item'].concat(categories.map((item) => item.name) ?? [''])}
        handleChangeCate={handleChangeCate}
      />
      {handleRenderUI()}
    </div>
  );
};

export default SectionMagazine1;
