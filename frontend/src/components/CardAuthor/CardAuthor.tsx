import React, { FC } from 'react';
import Avatar from '@/components/Avatar/Avatar';
import { TAuth<PERSON> } from '@/contains/author';
import { IMG_PLACEHOLDER } from '@/contains/contants';
import { LinkRoute } from '@/components/LinkRoute';

export interface CardAuthorProps {
  className?: string;
  author: TAuthor;
}

const CardAuthor: FC<CardAuthorProps> = ({ className = "", author }) => {
  const {username, image,last_name,first_name} = author
  const fullName: string = `${first_name ?? ''} ${last_name ?? ''}`.trim() || 'Author';

  return (
    <LinkRoute
      href={`/author/${username}`}
      className={`nc-CardAuthor flex items-center ${className}`}
    >
      <Avatar
        sizeClass="h-10 w-10 text-base"
        containerClassName="flex-shrink-0 me-4"
        radius="rounded-full"
        imgUrl={image??IMG_PLACEHOLDER}
        userName={fullName}
      />
      <div>
        <h2
          className={`text-sm sm:text-base text-neutral-900 dark:text-neutral-100 font-medium sm:font-semibold`}
        >
          {fullName}
        </h2>
        <span
          className={`block mt-[2px] text-xs text-neutral-500 dark:text-neutral-400`}
        >
          {username}
        </span>
      </div>
    </LinkRoute>
  );
};

export default CardAuthor;
