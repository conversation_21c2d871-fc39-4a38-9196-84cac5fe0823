'use client';

import React, { FC } from 'react';
import Avatar from '@/components/Avatar/Avatar';
import NcDropDown, { NcDropDownItem } from '@/components/NcDropDown/NcDropDown';
import twFocusClass from '@/utils/twFocusClass';
import { IMG_PLACEHOLDER } from '@/contains/contants';

export interface CommentCardProps {
  className?: string;
  size?: 'large' | 'normal';
}

const actions: NcDropDownItem[] = [
  {
    id: 'edit',
    name: 'Chỉnh sửa',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
    <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10" />
  </svg>`
  },
  {
    id: 'delete',
    name: 'Xóa',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
    <path stroke-linecap="round" stroke-linejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
  </svg>
  `
  }
];
const CommentCardSkeleton: FC<CommentCardProps> = ({
  className = '',
  size = 'large'
}: CommentCardProps) => {
  return (
    <>
      <div className={`nc-CommentCard flex ${className}`}>
        <Avatar
          sizeClass={`h-6 w-6 text-base ${
            size === 'large' ? 'sm:text-lg sm:h-8 sm:w-8' : ''
          }`}
          imgUrl={IMG_PLACEHOLDER}
          radius="rounded-full"
          containerClassName="mt-4"
        />
        <div className="flex-grow flex flex-col p-4 ms-2 text-sm border border-neutral-200 rounded-xl sm:ms-3 sm:text-base dark:border-neutral-700">
          {/* AUTHOR INFOR */}
          <div className="relative flex items-center pe-6">
            <div className="absolute -end-3 -top-3">
              <NcDropDown
                className={`p-2 text-neutral-500 flex items-center justify-center rounded-lg hover:text-neutral-800 dark:hover:text-neutral-200 sm:hover:bg-neutral-100 dark:hover:bg-neutral-800 ${twFocusClass()}`}
                data={actions}
                onClick={()=>{}}
              />
            </div>
            <p
              className="flex-shrink-0 font-semibold text-neutral-800 dark:text-neutral-100"
            >
              Tác giả
            </p>
          </div>

          {/* CONTENT */}
          <span className="block text-neutral-700 mt-2 mb-3 sm:mt-3 sm:mb-4 dark:text-neutral-300">
            <div className="mb-1 h-2 w-45 rounded-full bg-gray-300">&nbsp;</div>
            <div className="mb-1 h-2 w-45 rounded-full bg-gray-300">&nbsp;</div>
          </span>
        </div>
      </div>
    </>
  );
};

export default CommentCardSkeleton;
