'use client';

import React, { FC, useRef, useState } from 'react';
import Avatar from '@/components/Avatar/Avatar';
import NcDropDown, { NcDropDownItem } from '@/components/NcDropDown/NcDropDown';
import twFocusClass from '@/utils/twFocusClass';
import ModalEditComment from './ModalEditComment';
import ModalDeleteComment from './ModalDeleteComment';
import { TCommentItem } from '@/contains/post';
import { IMG_PLACEHOLDER } from '@/contains/contants';
import { useCommentStore } from '@/stores/useCommentStore';
import { useUserServer } from '@/hooks/useUser';
import CommentCardLikeReply from '@/components/CommentCardLikeReply/CommentCardLikeReply';
import SingleCommentForm from '@/app/(singles)/SingleCommentForm';

export interface CommentCardProps {
  className?: string;
  comment: TCommentItem;
  size?: 'large' | 'normal';
  postId: number;
  isHideReply?: boolean;
}

const actions: NcDropDownItem[] = [
  {
    id: 'edit',
    name: 'Chỉnh sửa',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
    <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10" />
  </svg>`
  },
  {
    id: 'delete',
    name: 'Xóa',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
    <path stroke-linecap="round" stroke-linejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
  </svg>
  `
  }
];
const CommentCard: FC<CommentCardProps> = ({
  className = '',
  comment,
  size = 'large',
  postId,
  isHideReply = false
}: CommentCardProps) => {
  const {
    author,
    content,
    is_liked,
    likes_count,
    replies,
  } = comment;
  const { setIsDelete, setIsEdit } = useCommentStore();
  const { data: user } = useUserServer();
  const [isEditting, setIsEditting] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const textareaRef = useRef(null);
  const [isReplying, setIsReplying] = useState(false);

  const openReplyForm = () => {
    setIsReplying(true);
    setTimeout(() => {
      textareaRef.current && ( textareaRef.current as any ).focus();
    }, 100);
  };
  const closeReplyForm = () => {
    setIsReplying(false);
  };

  const openModalEditComment = () => setIsEditting(true);
  const closeModalEditComment = () => setIsEditting(false);

  const openModalDeleteComment = () => setIsDeleting(true);
  const closeModalDeleteComment = () => setIsDeleting(false);

  const hanldeClickDropDown = (item: ( typeof actions )[number]) => {

    if (item.id === 'edit') {
      setIsEdit(true);
      return openModalEditComment();
    }
    if (item.id === 'delete') {
      setIsDelete(true);
      return openModalDeleteComment();
    }
    return;
  };

  const renderCommentForm = () => {
    return (
      <SingleCommentForm
        textareaRef={textareaRef}
        onClickSubmit={closeReplyForm}
        onClickCancel={closeReplyForm}
        className="flex-grow"
        isReply={isReplying}
        postId={postId}
        comment={comment}
      />
    );
  };

  return (
    <>
      <div className={`nc-CommentCard flex ${className}`}>
        <Avatar
          sizeClass={`h-6 w-6 text-base ${
            size === 'large' ? 'sm:text-lg sm:h-8 sm:w-8' : ''
          }`}
          imgUrl={author?.image ?? IMG_PLACEHOLDER}
          radius="rounded-full"
          containerClassName="mt-4"
        />
        <div className="flex-grow flex flex-col p-4 ms-2 text-sm border border-neutral-200 rounded-xl sm:ms-3 sm:text-base dark:border-neutral-700">
          {/* AUTHOR INFOR */}
          <div className="relative flex items-center pe-6">
            {user?.data?.id.toString() === author?.id.toString() && <div className="absolute -end-3 -top-3">
              <NcDropDown
                className={`p-2 text-neutral-500 flex items-center justify-center rounded-lg hover:text-neutral-800 dark:hover:text-neutral-200 sm:hover:bg-neutral-100 dark:hover:bg-neutral-800 ${twFocusClass()}`}
                data={actions}
                onClick={hanldeClickDropDown}
              />
            </div>}

            <p
              className="flex-shrink-0 font-semibold text-neutral-800 dark:text-neutral-100"
            >
              {author?.full_name ?? 'Author'}
            </p>
          </div>

          {/* CONTENT */}
          <span className="block text-neutral-700 mt-2 mb-3 sm:mt-3 sm:mb-4 dark:text-neutral-300">
            {content}
          </span>
          {/* ACTION LIKE REPLY */}
          {( isReplying && user?.data?.id ) ? (
            renderCommentForm()
          ) : (
            <>
              {!isHideReply && <CommentCardLikeReply
                className={className}
                isLiked={is_liked}
                likeCount={likes_count}
                id={postId}
                onClickReply={openReplyForm}
                comment={comment}
                isCancelGetCountLike={true}
              />}
            </>
          )}
            {replies?.map((rep) => {
              return <CommentCard
                className='mt-4'
                key={rep.id}
                comment={rep}
                postId={postId}
                isHideReply={true}
              />;
            })}

        </div>
      </div>

      <ModalEditComment
        show={isEditting}
        comment={comment}
        onCloseModalEditComment={closeModalEditComment}
      />
      <ModalDeleteComment
        show={isDeleting}
        comment={comment}
        onCloseModalDeleteComment={closeModalDeleteComment}
      />
    </>
  );
};

export default CommentCard;
