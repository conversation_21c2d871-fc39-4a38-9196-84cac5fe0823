'use client';

import React, { FC, useEffect, useRef } from 'react';
import NcModal from '@/components/NcModal/NcModal';
import ButtonPrimary from '@/components/Button/ButtonPrimary';
import ButtonSecondary from '@/components/Button/ButtonSecondary';
import ButtonThird from '../Button/ButtonThird';
import { TCommentItem } from '@/contains/post';
import { toast } from 'react-toastify';
import { useCommentStore } from '@/stores/useCommentStore';
import { useForm } from 'react-hook-form';
import { schemaSubmitComment, SubmitCommentFormValues } from '@/data/validation';
import { yupResolver } from '@hookform/resolvers/yup';

export interface ModalDeleteCommentProps {
  show: boolean;
  onCloseModalDeleteComment: () => void;
  comment: TCommentItem;
}

const ModalDeleteComment: FC<ModalDeleteCommentProps> = ({
  show,
  onCloseModalDeleteComment,
  comment
}) => {
  const textareaRef = useRef(null);
  const {
    handleSubmit,
  } = useForm({});
  const { deleteComment } = useCommentStore();

  const handleClickSubmitForm = async () => {
    try {
      await deleteComment({
        id: comment?.id.toString() ?? ''
      });
    } catch {
      toast.error('Xóa Bình luận không thành công');
    }
  };

  useEffect(() => {
    if (show) {
      setTimeout(() => {
        const element: HTMLTextAreaElement | null = textareaRef.current;
        if (element) {
          ( element as HTMLTextAreaElement ).focus();
        }
      }, 400);
    }
  }, [show]);

  const renderContent = () => {
    return (
      <form onSubmit={handleSubmit(handleClickSubmitForm)}>
        <h3 className="text-lg font-semibold text-neutral-900 dark:text-neutral-200">
          Xóa Bình Luận
        </h3>
        <span className="text-sm">
          Bạn có chắc muốn xóa bình luận này không? Bạn không thể hoàn tác hành động này.
        </span>
        <div className="mt-4 space-x-3 rtl:space-x-reverse">
          <ButtonPrimary
            className="!bg-red-500"
            type="submit"
          >
            Xóa
          </ButtonPrimary>
          <ButtonThird type="button" onClick={onCloseModalDeleteComment}>
            Hủy
          </ButtonThird>
        </div>
      </form>
    );
  };

  const renderTrigger = () => {
    return null;
  };

  return (
    <NcModal
      isOpenProp={show}
      onCloseModal={onCloseModalDeleteComment}
      contentExtraClass="max-w-screen-sm"
      renderContent={renderContent}
      renderTrigger={renderTrigger}
      modalTitle=""
    />
  );
};

export default ModalDeleteComment;
