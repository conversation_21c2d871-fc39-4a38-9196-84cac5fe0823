import React, { FC } from 'react';
import PostCardSaveAction from '@/components/PostCardSaveAction/PostCardSaveAction';
import PostCardLikeAndComment from '@/components/PostCardLikeAndComment/PostCardLikeAndComment';
import PostTypeFeaturedIcon from '@/components/PostTypeFeaturedIcon/PostTypeFeaturedIcon';
import Image from 'next/image';
import { Post } from '@/contains/post';
import { IMG_PLACEHOLDER } from '@/contains/contants';
import CategoryBadgeList from '@/components/CategoryBadgeList/CategoryBadgeList';
import { LinkRoute } from '@/components/LinkRoute';
import { DateFormat } from '@/components/DateFormat';

export interface Card9Props {
  className?: string;
  ratio?: string;
  post: Post;
  hoverClass?: string;
}

const Card9: FC<Card9Props> = ({
  className = 'h-full',
  ratio = 'aspect-w-3 aspect-h-3 sm:aspect-h-4',
  post,
  hoverClass = ''
}) => {
  const { name, categories, slug, image, author, updated_at } = post;
  const fullName: string = `${author?.first_name ?? ''} ${author?.last_name ?? ''}`.trim() || 'Author';
  const renderMeta = () => {
    return (
      <div className="inline-flex items-center text-xs text-neutral-300">
        <div className="block ">
          <h2 className="block text-base sm:text-lg font-semibold text-white ">
            <span className="line-clamp-2" title={name}>
              {name}
            </span>
          </h2>
          <LinkRoute href={'/'} className="flex mt-2.5 relative">
            <span className="block text-neutral-200 hover:text-white font-medium truncate">
              {fullName}
            </span>
            <span className="mx-[6px] font-medium">·</span>
            <span className="font-normal truncate"><DateFormat date={updated_at} /></span>
          </LinkRoute>
        </div>
      </div>
    );
  };

  return (
    <div
      className={`nc-Card9 relative flex flex-col group rounded-3xl overflow-hidden z-0 ${hoverClass} ${className}`}
    >
      <div className="absolute inset-x-0 top-0 p-3 flex items-center justify-between transition-all opacity-0 z-[-1] group-hover:opacity-100 group-hover:z-10 duration-300">
        <PostCardLikeAndComment
          className="relative"
          id={post.id}
          liked={post.is_liked}
          isCancelGetCountLike={true}
          likeCount={post.likes_count}
          comments_count={post.comments_count}
        />
        <PostCardSaveAction hidenReadingTime className="relative" />
      </div>
      <div className={`flex items-start relative w-full ${ratio}`}></div>
      <LinkRoute href={`/${slug}` ?? '/'}>
        <Image
          fill
          alt=""
          className="object-cover w-full h-full rounded-3xl"
          src={image ?? IMG_PLACEHOLDER}
          sizes="(max-width: 600px) 480px, 500px"
        />
        <PostTypeFeaturedIcon
          className="absolute top-3 left-3 group-hover:hidden"
          postType={'standard'}
          wrapSize="w-7 h-7"
          iconSize="w-4 h-4"
        />
        <span className="absolute inset-0 bg-black bg-opacity-10 opacity-0 group-hover:opacity-100 transition-opacity"></span>
      </LinkRoute>
      <LinkRoute
        href={`/${slug}` ?? '/'}
        className="absolute bottom-0 inset-x-0 h-1/2 bg-gradient-to-t from-black opacity-50"
      />
      <div className="absolute bottom-0 inset-x-0 p-4 flex flex-col flex-grow">
        <LinkRoute href={`/${slug}` ?? '/'} className="absolute inset-0"></LinkRoute>
        <div className="mb-3">
          <CategoryBadgeList categories={categories} />
        </div>
        {renderMeta()}
      </div>
    </div>
  );
};

export default Card9;
