import React, { FC } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { IconProp } from '@fortawesome/fontawesome-svg-core';

export interface SocialItemEntry {
  key: string;
  value: string | null;
}

export interface SocialsList1Props {
  className?: string;
  socialList?: Array<Array<SocialItemEntry>>;
}

const renderIconFromString = (socialIcon: {iconStr: string, color: string, backgroundColor: string}) => {
  const { iconStr, color, backgroundColor } = socialIcon;
  const parts = iconStr?.trim().split(' ');
  if (parts?.length === 2) {
    const icon: IconProp = [parts[0] as any, parts[1] as any] as IconProp;
    return <FontAwesomeIcon
      icon={icon}
      color={color}
      style={{
        backgroundColor: backgroundColor
      }}
      className='w-5 h-5 p-[2px]'
    />;
  }
  return null;
};

const SocialsList1: FC<SocialsList1Props> = ({ className = 'space-y-2.5', socialList }) => {
  const parseItem = (entry: SocialItemEntry[]) => {
    const result: Record<string, string> = {};
    entry.forEach(({ key, value }) => {
      if (value) {
        result[key] = value;
      }
    });
    return result;
  };

  const renderItem = (entry: SocialItemEntry[], index: number) => {
    const { name, icon, url, color, ['background-color']: backgroundColor } = parseItem(entry);

    return (
      <a
        key={index}
        href={url}
        target="_blank"
        rel="noopener noreferrer"
        className="flex items-center text-2xl leading-none space-x-3 rtl:space-x-reverse"
      >
        {renderIconFromString({
          iconStr: icon,
          color: color,
          backgroundColor: backgroundColor
        })}
        <span className="hidden lg:block text-sm">{name}</span>
      </a>
    );
  };

  return (
    <div className={`nc-SocialsList1 ${className}`} data-nc-id="SocialsList1">
      {socialList?.map(renderItem)}
    </div>
  );
};

export default SocialsList1;
