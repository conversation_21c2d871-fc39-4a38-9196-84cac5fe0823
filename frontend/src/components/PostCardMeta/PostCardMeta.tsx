import React, { FC } from 'react';
import Avatar from '@/components/Avatar/Avatar';
import { Post } from '@/contains/post';
import { LinkRoute } from '@/components/LinkRoute';
import { DateFormat } from '@/components/DateFormat';

export interface PostCardMetaProps {
  className?: string;
  meta: Post;
  hiddenAvatar?: boolean;
  avatarSize?: string;
}

const PostCardMeta: FC<PostCardMetaProps> = ({
  className = 'leading-none text-xs',
  meta,
  hiddenAvatar = false,
  avatarSize = 'h-7 w-7 text-sm'
}) => {
  const { author, updated_at } = meta;
  const { image, first_name, last_name } = author;
  const fullName: string = `${first_name ?? ''} ${last_name ?? ''}`.trim() || 'Author';

  return (
    <div
      className={`nc-PostCardMeta inline-flex items-center flex-wrap text-neutral-800 dark:text-neutral-200 ${className}`}
    >
      <LinkRoute
        href={author.username ? `/author/${author.username}` : '/'}
        className="relative flex items-center space-x-2 rtl:space-x-reverse"
      >
        {!hiddenAvatar && (
          <Avatar
            radius="rounded-full"
            sizeClass={avatarSize}
            imgUrl={image}
            userName={fullName}
          />
        )}
        <span className="block text-neutral-700 hover:text-black dark:text-neutral-300 dark:hover:text-white font-medium">
          {fullName}
        </span>
      </LinkRoute>
      <>
        <span className="text-neutral-500 dark:text-neutral-400 mx-[6px] font-medium">
          ·
        </span>
        <span className="text-neutral-500 dark:text-neutral-400 font-normal">
          <DateFormat date={updated_at} />
        </span>
      </>
    </div>
  );
};

export default PostCardMeta;
