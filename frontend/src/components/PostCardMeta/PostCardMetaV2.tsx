import React, { FC } from 'react';
import Avatar from '@/components/Avatar/Avatar';
import { Post } from '@/contains/post';
import { IMG_PLACEHOLDER } from '@/contains/contants';
import { LinkRoute } from '@/components/LinkRoute';
import { DateFormat } from '@/components/DateFormat';

export interface PostCardMetaV2Props {
  meta: Post;
  hiddenAvatar?: boolean;
  className?: string;
  titleClassName?: string;
  avatarSize?: string;
}

const PostCardMetaV2: FC<PostCardMetaV2Props> = ({
  meta,
  hiddenAvatar = false,
  className = 'leading-none text-xs',
  titleClassName = 'text-base',
  avatarSize = 'h-9 w-9 text-base'
}) => {
  const { name, author, updated_at } = meta;
  const fullName: string = `${author?.first_name ?? ''} ${author?.last_name ?? ''}`.trim() || 'Author';
  return (
    <div
      className={`nc-PostCardMetaV2 inline-flex items-center flex-wrap text-neutral-800 dark:text-neutral-200 ${className}`}
    >
      <div className="relative flex items-center space-x-2 rtl:space-x-reverse">
        {!hiddenAvatar && (
          <Avatar
            radius="rounded-full"
            sizeClass={avatarSize}
            imgUrl={author.image ?? IMG_PLACEHOLDER}
            userName={fullName}
          />
        )}
        <div>
          <h2 className={`block font-semibold ${titleClassName}`}>
            <LinkRoute href={`/${meta.slug}`} className="line-clamp-1">
              {name}
            </LinkRoute>
          </h2>
          <LinkRoute href={author.username ? `/author/${author.username}` : '/'} className="flex mt-1.5">
            <span className="block text-neutral-700 hover:text-black dark:text-neutral-300 dark:hover:text-white font-medium">
              {fullName}
            </span>
            <span className="text-neutral-500 dark:text-neutral-400 mx-[6px] font-medium">
              ·
            </span>
            <span className="text-neutral-500 dark:text-neutral-400 font-normal">
              <DateFormat date={updated_at} />
            </span>
          </LinkRoute>
        </div>
      </div>
    </div>
  );
};

export default PostCardMetaV2;
