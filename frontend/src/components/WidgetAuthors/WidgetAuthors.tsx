import CardAuthor from '@/components/CardAuthor/CardAuthor';
import WidgetHeading1 from '@/components/WidgetHeading1/WidgetHeading1';
import { TAuthor } from '@/contains/author';
import { authorApi } from '@/apis/authorApi';
import Nodata from '@/components/Nodata';

export interface WidgetAuthorsProps {
  className?: string;
}

async function fetchAuthors() {
  try {
    const { data, error } = await authorApi.get<TAuthor[]>({
      params: { page: 1, per_page: 5 }
    });
    return !error ? data ?? [] : [];
  } catch (err) {
    console.error('Failed to fetch authors', err);
    return [];
  }
}

const WidgetAuthors = async ({ className = 'bg-neutral-100 dark:bg-neutral-800' }: WidgetAuthorsProps) => {
  const authors = await fetchAuthors();

  return (
    <div className={`nc-WidgetAuthors rounded-3xl overflow-hidden ${className}`}>
      <WidgetHeading1 title="👤 Khám phá các tác giả" />
      <div className="flow-root">
        <div className="flex flex-col divide-y divide-neutral-200 dark:divide-neutral-700">
          {authors.length > 0 ? (
            authors.map((author) => (
              <CardAuthor
                key={author.id}
                author={author}
                className="p-4 xl:p-5 hover:bg-neutral-200 dark:hover:bg-neutral-700"
              />
            ))
          ) : (
            <Nodata />
          )}
        </div>
      </div>
    </div>
  );
};

export default WidgetAuthors;
