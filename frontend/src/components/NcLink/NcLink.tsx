import { Route } from '@/routers/types';
import React, { FC, ReactNode } from 'react';
import { LinkRoute } from '@/components/LinkRoute';

export interface NcLinkProps {
  className?: string;
  colorClass?: string;
  href: Route;
  children: ReactNode;
}

const NcLink: FC<NcLinkProps> = ({
  className = "font-medium",
  colorClass = "text-primary-6000 hover:text-primary-800 dark:text-primary-500 dark:hover:text-primary-6000",
  children,
  href,
}) => {
  return (
    <LinkRoute className={`nc-NcLink ${colorClass} ${className}`} href={href}>
      {children}
    </LinkRoute>
  );
};

export default NcLink;
