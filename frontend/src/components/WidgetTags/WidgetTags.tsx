import Tag from '@/components/Tag/Tag';
import WidgetHeading1 from '@/components/WidgetHeading1/WidgetHeading1';
import { tagApi } from '@/apis/tagApi';
import { TTag } from '@/contains/tag';
import { Route } from '@/routers/types';
import { ENDPOINT } from '@/contains/endpoint';
import Nodata from '@/components/Nodata';

export interface WidgetTagsProps {
  className?: string;
}

async function fetchTags() {
  try {
    const { data, error } = await tagApi.get<TTag[]>({
      endpoint: ENDPOINT.GET_TAGS_FILTERS_WITH_SCORE,
      params: {
        order_by: 'total_score',
        page: 1,
        per_page: 15,
      }
    });

    return !error ? data ?? [] : [];
  } catch (err) {
    console.error('Failed to fetch tags', err);
    return [];
  }
}

const WidgetTags = async ({ className = 'bg-neutral-100 dark:bg-neutral-800' }: WidgetTagsProps) => {
  const tags = await fetchTags();

  return (
    <div className={`nc-WidgetTags rounded-3xl overflow-hidden ${className}`}>
      <WidgetHeading1
        title="💡 Xem thêm các thẻ"
        viewAll={{ label: 'Xem tất cả', href: '/tag' as Route }}
      />
      <div className="flex flex-wrap p-4 xl:p-5">
        {tags.length > 0 ? (
          tags.map((tag) => (
            <Tag className="mr-2 mb-2" tag={tag} key={tag.id} />
          ))
        ) : (
          <Nodata />
        )}
      </div>
    </div>
  );
};

export default WidgetTags;
