import React, { <PERSON> } from 'react';
import { TTag } from '@/contains/tag';
import { LinkRoute } from '@/components/LinkRoute';

export interface TagProps {
  className?: string;
  tag: TTag;
  hideCount?: boolean;
}

const Tag: FC<TagProps> = ({ className = '', tag, hideCount = false }) => {
  const name = tag.name ?? '';
  const slug = tag.slug ?? '';
  const posts_count = tag.posts_count ?? 0;
  return (
    <LinkRoute
      className={`nc-Tag inline-block bg-white hover:bg-neutral-50 text-sm text-neutral-600 dark:text-neutral-300 py-2 px-3 rounded-lg md:py-2.5 md:px-4 dark:bg-neutral-900 ${className}`}
      href={`/tag/${slug}`}
    >
      {`${name}`}
      {!hideCount && (
        <span className="text-xs font-normal"> ({posts_count})</span>
      )}
    </LinkRoute>
  );
};

export default Tag;
