'use client';

import Input from '@/components/Input/Input';
import ButtonCircle from '@/components/Button/ButtonCircle';
import { ArrowRightIcon } from '@heroicons/react/24/solid';
import React, { FormEvent, useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useRecentSearchStore } from '@/stores/useRecentSearchStore'; // 👈 import store

export const SearchContainer = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [searchValue, setSearchValue] = useState(searchParams.get('search') ?? '');

  const { addRecentSearch } = useRecentSearchStore();

  useEffect(() => {
    const urlSearchValue = searchParams.get('search') ?? '';
    setSearchValue(urlSearchValue);
  }, [searchParams]);

  const handleSearchSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    const keyword = searchValue.trim();
    if (keyword) {
      addRecentSearch(keyword);
    }

    const params = new URLSearchParams(searchParams.toString());
    params.set('search', keyword);
    params.set('page', '1');

    router.push(`/search?${params.toString()}`);
  };

  return (
    <form
      className="relative w-full mt-8 sm:mt-11 text-left"
      onSubmit={handleSearchSubmit}
    >
      <label
        htmlFor="search-input"
        className="text-neutral-500 dark:text-neutral-300"
      >
        <span className="sr-only">Search all icons</span>
        <Input
          id="search-input"
          name="search"
          type="search"
          placeholder="Type and press enter"
          sizeClass="pl-14 py-5 pe-5 md:ps-16"
          defaultValue={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
        />
        <ButtonCircle
          className="absolute end-2.5 top-1/2 transform -translate-y-1/2"
          size="w-11 h-11"
          type="submit"
        >
          <ArrowRightIcon className="w-5 h-5 rtl:rotate-180" />
        </ButtonCircle>
        <span className="absolute start-5 top-1/2 transform -translate-y-1/2 text-2xl md:start-6">
          <svg width="24" height="24" fill="none" viewBox="0 0 24 24">
            <path
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="1.5"
              d="M19.25 19.25L15.5 15.5M4.75 11C4.75 7.54822 7.54822 4.75 11 4.75C14.4518 4.75 17.25 7.54822 17.25 11C17.25 14.4518 14.4518 17.25 11 17.25C7.54822 17.25 4.75 14.4518 4.75 11Z"
            ></path>
          </svg>
        </span>
      </label>
    </form>
  );
};
